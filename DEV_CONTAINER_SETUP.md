# 🚀 MVP ScoreWidget Dev Container Setup

This guide will help you set up the complete development environment with Code2Prompt and enhanced report generation capabilities using VS Code Dev Containers.

## 📋 Prerequisites

Before starting, ensure you have:

1. **Docker Desktop** installed and running
   - [Download Docker Desktop](https://www.docker.com/products/docker-desktop/)
   - Ensure Docker is running before proceeding

2. **Visual Studio Code** with Dev Containers extension
   - [Download VS Code](https://code.visualstudio.com/)
   - Install the [Dev Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)

3. **Git** for version control
   - [Download Git](https://git-scm.com/downloads)

## 🛠 Setup Instructions

### Step 1: Clone and Open Project

```bash
# Clone the repository
git clone <your-repo-url>
cd MVPScoreWidget

# Open in VS Code
code .
```

### Step 2: Open in Dev Container

When you open the project in VS Code, you should see a notification:

> **"Folder contains a Dev Container configuration file. Reopen folder to develop in a container"**

Click **"Reopen in Container"**

**Alternative methods:**

1. **Command Palette**: 
   - Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)
   - Type "Dev Containers: Reopen in Container"
   - Press Enter

2. **Status Bar**:
   - Click the blue button in the bottom-left corner of VS Code
   - Select "Reopen in Container"

### Step 3: Wait for Container Build

The first time you open the dev container, it will:

1. **Build the Docker image** (5-10 minutes)
   - Downloads base Node.js image
   - Installs Rust and Code2Prompt
   - Sets up all development tools

2. **Install dependencies** (2-3 minutes)
   - Runs `npm install`
   - Installs Chrome for Puppeteer
   - Sets up environment

3. **Configure environment** (1 minute)
   - Runs post-creation scripts
   - Sets up templates and scripts
   - Verifies installations

**Total setup time: ~10-15 minutes**

### Step 4: Configure Environment Variables

After the container is ready:

1. **Copy the environment template**:
   ```bash
   cp .env .env.local
   ```

2. **Edit the .env file** with your actual values:
   ```bash
   # Open in VS Code editor
   code .env
   ```

3. **Required variables**:
   ```env
   # OpenAI Configuration (Required for enhanced reports)
   OPENAI_API_KEY=sk-your-actual-openai-api-key

   # Database Configuration
   DATABASE_URL=your_database_url_here

   # Stripe Configuration (for payments)
   STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
   STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
   STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

   # Development Configuration
   NODE_ENV=development
   PORT=5000
   ```

## 🧪 Verify Installation

### Test Code2Prompt

```bash
# Check Code2Prompt version
code2prompt --version

# Run the test script
./scripts/test-enhanced-report.sh
```

Expected output:
```
✅ Code2Prompt is installed: code2prompt 4.0.0
🧪 Testing Code2Prompt...
✅ Code2Prompt test successful!
📄 Output saved to /tmp/project-analysis.txt
📊 File size: 2.1M
📝 Line count: 45,234
```

### Test Development Server

```bash
# Start development server
npm run dev
```

Expected output:
```
🚀 Starting MVP ScoreWidget development environment...
Server running on http://localhost:5000
Frontend available at http://localhost:3000
```

### Test Enhanced Report Generation

```bash
# Test with current project
node -e "
const { generateCodebasePrompt } = require('./server/code2prompt-integration.ts');
generateCodebasePrompt('.', { analysisType: 'FULL' })
  .then(result => console.log('✅ Enhanced analysis ready:', result.coveragePercentage + '% coverage'))
  .catch(err => console.error('❌ Error:', err));
"
```

## 🎯 Development Workflow

### 1. Daily Development

```bash
# Start development (with all checks)
./scripts/dev-start.sh

# Or start individual services
npm run dev          # Development server
npm run check        # TypeScript checking
npm run db:push      # Database schema updates
```

### 2. Enhanced Report Testing

```bash
# Generate comprehensive codebase analysis
code2prompt . \
  --template .code2prompt/templates/comprehensive.hbs \
  --exclude "node_modules/**,dist/**,build/**,*.log" \
  --output analysis.txt \
  --tokens

# Test enhanced report generation
curl -X POST http://localhost:5000/api/generate-enhanced-report \
  -H "Content-Type: application/json" \
  -d '{"analysisId": 1, "projectPath": "/workspaces/MVPScoreWidget"}'
```

### 3. Code Quality Checks

```bash
# TypeScript checking
npm run check

# Format code (Prettier is configured to run on save)
npx prettier --write .

# Lint code
npx eslint server/ client/src/
```

## 📊 Code2Prompt Usage Examples

### Basic Project Analysis

```bash
# Analyze entire project with smart filtering
code2prompt . \
  --exclude "node_modules/**,dist/**,build/**,*.log,.git/**" \
  --line-numbers \
  --tokens \
  --output project-analysis.txt
```

### Security-Focused Analysis

```bash
# Use security template
code2prompt . \
  --template .code2prompt/templates/security.hbs \
  --include "**/*.ts,**/*.js,**/*.json" \
  --exclude "node_modules/**,dist/**" \
  --output security-analysis.txt
```

### Architecture Analysis

```bash
# Focus on core architecture files
code2prompt . \
  --include "server/**/*.ts,client/src/**/*.tsx,shared/**/*.ts" \
  --exclude "**/*.test.ts,**/*.spec.ts" \
  --template .code2prompt/templates/comprehensive.hbs \
  --output architecture-analysis.txt
```

## 🔧 Available Services

The dev container includes these services:

| Service | Port | Description |
|---------|------|-------------|
| **Frontend** | 3000 | Vite development server |
| **Backend** | 5000 | Express API server |
| **PostgreSQL** | 5432 | Local database |
| **Redis** | 6379 | Caching and sessions |
| **Adminer** | 8081 | Database management UI |

### Access Services

```bash
# Frontend
open http://localhost:3000

# Backend API
curl http://localhost:5000/api/health

# Database UI (Adminer)
open http://localhost:8081
# Server: postgres, User: postgres, Password: postgres, Database: mvp_scorewidget
```

## 🎨 VS Code Features

### Installed Extensions

- **Augment Code** - AI-powered coding assistant
- **TypeScript** - Enhanced TypeScript support
- **Tailwind CSS** - IntelliSense for Tailwind
- **Prettier** - Code formatting
- **ESLint** - Code quality
- **GitLens** - Enhanced Git integration
- **React** - React development tools

### Useful Commands

```bash
# Command Palette shortcuts
Ctrl+Shift+P (Cmd+Shift+P on Mac)

# Useful commands:
- "TypeScript: Restart TS Server"
- "Prettier: Format Document"
- "Git: View History"
- "Augment: Generate Code"
```

## 🐛 Troubleshooting

### Container Won't Start

```bash
# Check Docker is running
docker ps

# Rebuild container
# In VS Code: Ctrl+Shift+P -> "Dev Containers: Rebuild Container"

# Check logs
docker logs <container-name>
```

### Code2Prompt Issues

```bash
# Reinstall Code2Prompt
cargo install code2prompt --force

# Check Rust installation
rustc --version
cargo --version

# Test with minimal example
echo "console.log('test')" > test.js
code2prompt . --include "test.js" --output test.txt
```

### Port Conflicts

```bash
# Check what's using ports
lsof -i :3000
lsof -i :5000

# Kill processes if needed
kill -9 <PID>

# Or change ports in package.json and docker-compose.yml
```

### Environment Variables

```bash
# Check environment
printenv | grep -E "(NODE_ENV|OPENAI_API_KEY)"

# Reload environment
source ~/.bashrc

# Restart VS Code if needed
```

## 🚀 Next Steps

1. **Configure your API keys** in `.env`
2. **Test enhanced report generation** with Code2Prompt
3. **Explore the Augment Code extension** for AI-powered development
4. **Start building enhanced MVP reports** with 80-90% codebase coverage!

## 📚 Additional Resources

- [Code2Prompt Documentation](https://github.com/mufeedvh/code2prompt)
- [VS Code Dev Containers Guide](https://code.visualstudio.com/docs/devcontainers/containers)
- [Augment Code Extension](https://marketplace.visualstudio.com/items?itemName=augmentcode.augment)
- [Enhanced Report Generation Guide](./docs/enhanced-report-generation.md)

---

**🎉 You're all set!** Your development environment now includes Code2Prompt for comprehensive codebase analysis and enhanced OpenAI models for generating detailed MVP reports with 80-90% coverage.
