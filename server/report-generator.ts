import { FullReportData } from "@shared/schema";
import { Analysis } from "@shared/schema";
import { generateAndSavePDF, generatePDFBuffer } from "./pdf-generator";
import {
  makeOpenAIJSONRequest,
  makeOpenAITextRequest,
  makeOpenAIRequest,
  createEnhancedPrompt,
  ModelType
} from "./openai-utils";
import { generateCodebasePrompt, Code2PromptOptions } from "./code2prompt-integration";

// OpenAI utilities are now handled in openai-utils.ts

/**
 * Enhanced report generation with 80-90% codebase coverage using Code2Prompt and advanced models
 */
export async function generateEnhancedFullReport(
  analysis: Analysis,
  projectPath?: string,
  options: {
    useAdvancedModels?: boolean;
    useCode2Prompt?: boolean;
    analysisDepth?: 'BASIC' | 'COMPREHENSIVE' | 'DEEP';
  } = {}
): Promise<FullReportData> {
  const {
    useAdvancedModels = true,
    useCode2Prompt = false,
    analysisDepth = 'COMPREHENSIVE'
  } = options;

  // First, validate that we have the necessary data
  if (!analysis.scoreData || !analysis.projectInfo) {
    throw new Error("Cannot generate enhanced report: Score data or project info missing");
  }

  try {
    let enhancedContext: any = {};

    // Generate comprehensive codebase context using Code2Prompt if available
    if (useCode2Prompt && projectPath) {
      try {
        const code2promptResult = await generateCodebasePrompt(projectPath, {
          analysisType: 'FULL',
          tokenCount: true,
          lineNumbers: true,
          exclude: ['node_modules/**', '.git/**', 'dist/**', 'build/**', '*.log', '*.lock']
        });

        enhancedContext = {
          codebasePrompt: code2promptResult.prompt,
          coveragePercentage: code2promptResult.coveragePercentage,
          filesCovered: code2promptResult.filesCovered,
          metadata: code2promptResult.metadata
        };

        console.log(`Code2Prompt analysis: ${code2promptResult.coveragePercentage}% coverage, ${code2promptResult.filesCovered.length} files`);
      } catch (error) {
        console.warn('Code2Prompt analysis failed, falling back to standard analysis:', error);
      }
    }

    // Use enhanced models for deeper analysis
    const modelType: ModelType = useAdvancedModels ? 'REASONING' : 'GENERAL';

    return await generateFullReportWithContext(analysis, enhancedContext, modelType, analysisDepth);

  } catch (error) {
    console.error("Error generating enhanced full report:", error);
    // Fallback to standard report generation
    return await generateFullReport(analysis);
  }
}

/**
 * Generate report with enhanced context and model selection
 */
async function generateFullReportWithContext(
  analysis: Analysis,
  enhancedContext: any,
  modelType: ModelType,
  analysisDepth: 'BASIC' | 'COMPREHENSIVE' | 'DEEP'
): Promise<FullReportData> {
  // Create empty report structure
  const fullReport: FullReportData = {
    executiveSummary: '',
    detailedScores: [],
    techStack: {
      technologies: [],
      pros: [],
      cons: [],
      recommendations: [],
      alternatives: []
    },
    feedbackStrategy: {
      currentScore: 0,
      gaps: '',
      guide: '',
      codeSnippet: '',
      bestPractices: [],
      tools: []
    },
    deploymentChecklist: {
      currentScore: 0,
      gaps: '',
      hosting: '',
      performance: '',
      security: '',
      monitoring: '',
      timeline: ''
    },
    testingRecommendations: {
      currentScore: 0,
      gaps: [],
      frameworks: [],
      sampleTests: '',
      userTestingPlan: ''
    },
    documentationPlan: {
      currentScore: 0,
      gaps: [],
      readmeTemplate: '',
      tools: [],
      examples: ''
    },
    benchmarking: {
      score: 0,
      averageScore: 0,
      strengths: [],
      weaknesses: []
    },
    resourceLibrary: {
      tutorials: [],
      tools: [],
      communities: [],
      templates: []
    },
    roadmap: {
      currentLevel: '',
      targetLevel: '',
      steps: [],
      timeline: ''
    },
    visualizations: {
      radarChartData: null,
      codeQualityMetrics: {},
      comparisonData: null
    }
  };

  // Get the scored data
  const scoreData = analysis.scoreData;
  const projectInfo = analysis.projectInfo;

  // Generate each section with enhanced context and models
  const [
    executiveSummary,
    detailedScores,
    techStack,
    feedbackStrategy,
    deploymentChecklist,
    testingRecommendations,
    documentationPlan,
    benchmarking,
    resourceLibrary,
    roadmap,
    visualizations
  ] = await Promise.all([
    generateExecutiveSummary(projectInfo, scoreData, enhancedContext, modelType),
    generateDetailedScores(projectInfo, scoreData, enhancedContext, modelType),
    generateTechStackAnalysis(projectInfo, scoreData, enhancedContext, modelType),
    generateFeedbackStrategy(projectInfo, scoreData, enhancedContext, modelType),
    generateDeploymentChecklist(projectInfo, scoreData, enhancedContext, modelType),
    generateTestingRecommendations(projectInfo, scoreData, enhancedContext, modelType),
    generateDocumentationPlan(projectInfo, scoreData, enhancedContext, modelType),
    generateBenchmarking(projectInfo, scoreData, enhancedContext, modelType),
    generateResourceLibrary(projectInfo, scoreData, enhancedContext, modelType),
    generateRoadmap(projectInfo, scoreData, enhancedContext, modelType),
    generateVisualizations(projectInfo, scoreData, enhancedContext, modelType)
  ]);

  // Combine all sections
  fullReport.executiveSummary = executiveSummary;
  fullReport.detailedScores = detailedScores;
  fullReport.techStack = techStack;
  fullReport.feedbackStrategy = feedbackStrategy;
  fullReport.deploymentChecklist = deploymentChecklist;
  fullReport.testingRecommendations = testingRecommendations;
  fullReport.documentationPlan = documentationPlan;
  fullReport.benchmarking = benchmarking;
  fullReport.resourceLibrary = resourceLibrary;
  fullReport.roadmap = roadmap;
  fullReport.visualizations = visualizations;

  return fullReport;
}

/**
 * Generates a full detailed report based on analysis results (original function)
 */
export async function generateFullReport(analysis: Analysis): Promise<FullReportData> {
  // First, validate that we have the necessary data
  if (!analysis.scoreData || !analysis.projectInfo) {
    throw new Error("Cannot generate full report: Score data or project info missing");
  }

  try {
    // Create empty report structure
    const fullReport: FullReportData = {
      executiveSummary: '',
      detailedScores: [],
      techStack: {
        technologies: [],
        pros: [],
        cons: [],
        recommendations: [],
        alternatives: []
      },
      feedbackStrategy: {
        currentScore: 0,
        gaps: '',
        guide: '',
        codeSnippet: '',
        bestPractices: [],
        tools: []
      },
      deploymentChecklist: {
        currentScore: 0,
        gaps: '',
        hosting: '',
        performance: '',
        security: '',
        monitoring: '',
        timeline: ''
      },
      testingRecommendations: {
        currentScore: 0,
        gaps: [],
        frameworks: [],
        sampleTests: '',
        userTestingPlan: ''
      },
      documentationPlan: {
        currentScore: 0,
        gaps: [],
        readmeTemplate: '',
        tools: [],
        examples: ''
      },
      benchmarking: {
        score: 0,
        averageScore: 0,
        strengths: [],
        weaknesses: []
      },
      resourceLibrary: {
        tutorials: [],
        tools: [],
        communities: [],
        templates: []
      },
      roadmap: {
        currentLevel: '',
        targetLevel: '',
        steps: [],
        timeline: ''
      },
      visualizations: {
        radarChartData: null,
        codeQualityMetrics: {},
        // performanceMetrics: {}, // Commented out - can't measure performance from static code
        comparisonData: null
      }
    };

    // Get the scored data
    const scoreData = analysis.scoreData;
    const projectInfo = analysis.projectInfo;
    
    // Generate each section of the report in parallel
    const [
      executiveSummary,
      detailedScores,
      techStack,
      feedbackStrategy,
      deploymentChecklist,
      testingRecommendations,
      documentationPlan,
      benchmarking,
      resourceLibrary,
      roadmap,
      visualizations
    ] = await Promise.all([
      generateExecutiveSummary(projectInfo, scoreData),
      generateDetailedScores(projectInfo, scoreData),
      generateTechStackAnalysis(projectInfo, scoreData),
      generateFeedbackStrategy(projectInfo, scoreData),
      generateDeploymentChecklist(projectInfo, scoreData),
      generateTestingRecommendations(projectInfo, scoreData),
      generateDocumentationPlan(projectInfo, scoreData),
      generateBenchmarking(projectInfo, scoreData),
      generateResourceLibrary(projectInfo, scoreData),
      generateRoadmap(projectInfo, scoreData),
      generateVisualizations(projectInfo, scoreData)
    ]);

    // Combine all sections
    fullReport.executiveSummary = executiveSummary;
    fullReport.detailedScores = detailedScores;
    fullReport.techStack = techStack;
    fullReport.feedbackStrategy = feedbackStrategy;
    fullReport.deploymentChecklist = deploymentChecklist;
    fullReport.testingRecommendations = testingRecommendations;
    fullReport.documentationPlan = documentationPlan;
    fullReport.benchmarking = benchmarking;
    fullReport.resourceLibrary = resourceLibrary;
    fullReport.roadmap = roadmap;
    fullReport.visualizations = visualizations;

    return fullReport;
  } catch (error) {
    console.error("Error generating full report:", error);
    throw new Error("Failed to generate full report");
  }
}

/**
 * Generate executive summary section
 */
async function generateExecutiveSummary(
  projectInfo: any,
  scoreData: any,
  enhancedContext?: any,
  modelType: ModelType = 'GENERAL'
): Promise<string> {
  // Enhanced prompt with additional context if available
  let prompt = `
Given the project analysis data:
- Project Name: ${projectInfo.name}
- Project Type: ${projectInfo.type || 'Web Application'}
- Score: ${scoreData.total}/40
- MVP Readiness Level: ${scoreData.category}
- Strengths: ${scoreData.strengths.join(', ')}
- Weaknesses: ${scoreData.weaknesses.join(', ')}`;

  // Add enhanced context if available
  if (enhancedContext?.metadata) {
    prompt += `
- Technologies Detected: ${enhancedContext.metadata.technologies?.join(', ') || 'Standard web technologies'}
- Code Coverage: ${enhancedContext.coveragePercentage || 'N/A'}% of codebase analyzed
- Code Quality Metrics: ${JSON.stringify(enhancedContext.metadata.codeMetrics || {})}`;
  }

  prompt += `

Generate a comprehensive executive summary for a Vibe Coder. Include:
- The project name, type, and score with context about the analysis depth.
- The MVP readiness level with a detailed explanation based on the codebase analysis.
- The top 3 strengths and top 3 areas for improvement, formatted as bullet points.
- Strategic insights for rapid iteration and market readiness.
Keep the tone concise, professional, and encouraging for rapid iteration.

Format the response as follows:
**Executive Summary**
**Project**: [Name], [Type], [Score]/40
**MVP Readiness**: [Level] - [Brief explanation]
**Top 3 Strengths**:
- [Strength 1]
- [Strength 2]
- [Strength 3]
**Top 3 Areas for Improvement**:
- [Area 1]
- [Area 2]
- [Area 3]
  `;

  try {
    const result = await makeOpenAITextRequest(prompt, modelType);
    return result || `Executive Summary for ${projectInfo.name}\n\nScore: ${scoreData.total}/40\n\nThis project is in the ${scoreData.category} stage. Please see the detailed sections for more information.`;
  } catch (error) {
    console.error("Error generating executive summary:", error);
    return `Executive Summary for ${projectInfo.name}\n\nScore: ${scoreData.total}/40\n\nThis project is in the ${scoreData.category} stage. Please see the detailed sections for more information.`;
  }
}

/**
 * Generate detailed scores section
 */
async function generateDetailedScores(
  projectInfo: any,
  scoreData: any,
  enhancedContext?: any,
  modelType: ModelType = 'GENERAL'
): Promise<Array<{
  name: string;
  score: number;
  explanation: string;
  examples: string;
  codeSnippet?: string;
  suggestions: string;
}>> {
  const criterionScores = scoreData.criterionScores;
  const detailedScores = [];

  for (const criterion of criterionScores) {
    const prompt = `
Given the project analysis data for criterion ${criterion.name}:
- Score: ${criterion.score}/5
- Explanation: ${criterion.feedback || criterion.description}
- Files Analyzed: ${projectInfo.files ? projectInfo.files.slice(0, 5).join(', ') : 'Various project files'}
- Code Issues: ${criterion.issues || 'See analysis details'}
- Technologies: ${projectInfo.technologies.join(', ')}

Generate a detailed breakdown for a Vibe Coder. Include:
- The criterion name, score, and explanation.
- Specific examples from the project (e.g., file names, code snippets).
- A code snippet for improvement if applicable.
- Actionable suggestions for improvement.
Keep the tone technical, actionable, and focused on rapid iteration.

Format your response as JSON with these fields: explanation, examples, codeSnippet (optional), suggestions

Example format:
{
  "explanation": "There is no clear mechanism for collecting user feedback, which is crucial for iterating based on user needs.",
  "examples": "In index.js, there's no endpoint for user surveys or feedback forms. The project lacks any visible feedback integration.",
  "codeSnippet": "// Add to index.js\\napp.post('/feedback', (req, res) => {\\n  const feedback = req.body;\\n  // Save feedback to database (e.g., Firebase)\\n  res.status(200).send('Feedback received');\\n});",
  "suggestions": "1. Implement a simple feedback form using Formik in React to collect user input. 2. Integrate a tool like Google Analytics to track user behavior passively."
}
    `;

    try {
      const result = await makeOpenAIJSONRequest(prompt, modelType);

      detailedScores.push({
        name: criterion.name,
        score: criterion.score,
        explanation: result.explanation || "No explanation available",
        examples: result.examples || "No examples available",
        codeSnippet: result.codeSnippet || "",
        suggestions: String(result.suggestions || "No suggestions available")
      });
    } catch (error) {
      console.error(`Error generating detailed score for ${criterion.name}:`, error);
      detailedScores.push({
        name: criterion.name,
        score: criterion.score,
        explanation: String(criterion.feedback || "No feedback available"),
        examples: "Unable to generate specific examples",
        suggestions: "Please review the criterion description for improvement ideas"
      });
    }
  }

  return detailedScores;
}

/**
 * Generate tech stack analysis section
 */
async function generateTechStackAnalysis(projectInfo: any, scoreData: any): Promise<{
  technologies: string[];
  pros: string[];
  cons: string[];
  recommendations: string[];
  alternatives: string[];
}> {
  const prompt = `
Given the project analysis data:
- Technologies: ${projectInfo.technologies.join(', ')}
// - Performance Metrics: ${scoreData.performanceMetrics || 'Load time analysis pending'} // Commented out - can't measure from static code
- Dependencies: ${projectInfo.dependencies || 'Standard dependencies detected'}

Generate a technology stack analysis for a Vibe Coder. Include:
- A list of detected technologies.
- Pros and cons of the stack for rapid development.
- Recommendations for optimization.
- Alternative tools if applicable.
Keep the tone technical and focused on scalability and speed.

Format your response as JSON with these fields: pros (array), cons (array), recommendations (array), alternatives (array)

Example format:
{
  "pros": [
    "Next.js enables fast iteration with server-side rendering",
    "Tailwind CSS speeds up UI development with utility classes",
    "TypeScript improves code reliability"
  ],
  "cons": [
    "Large Next.js bundles may impact performance if not optimized",
    "Tailwind CSS can lead to bloated CSS files if not purged properly"
  ],
  "recommendations": [
    "Lazy-load components in Next.js to improve load times",
    "Use next-purge-css to remove unused Tailwind styles"
  ],
  "alternatives": [
    "For smaller projects, consider PicoCSS for a lighter CSS framework"
  ]
}
  `;

  try {
    const result = await makeOpenAIJSONRequest(prompt);
    
    return {
      technologies: projectInfo.technologies,
      pros: result.pros,
      cons: result.cons,
      recommendations: result.recommendations,
      alternatives: result.alternatives
    };
  } catch (error) {
    console.error("Error generating tech stack analysis:", error);
    return {
      technologies: projectInfo.technologies,
      pros: ["Modern technologies", "Well-supported ecosystem"],
      cons: ["May have steep learning curve"],
      recommendations: ["Review documentation for best practices"],
      alternatives: ["Consider alternative frameworks if appropriate"]
    };
  }
}

/**
 * Generate feedback strategy section
 */
async function generateFeedbackStrategy(projectInfo: any, scoreData: any): Promise<{
  currentScore: number;
  gaps: string;
  guide: string;
  codeSnippet?: string;
  bestPractices: string[];
  tools: string[];
}> {
  // Find the feedback collection criterion
  const feedbackCriterion = scoreData.criterionScores.find((c: any) =>
    c.name.toLowerCase().includes('feedback'));
  
  const currentScore = feedbackCriterion ? feedbackCriterion.score : 0;
  
  const prompt = `
Given the project analysis data for Feedback Collection:
- Score: ${currentScore}/5
- Gaps: ${feedbackCriterion ? feedbackCriterion.feedback : 'No visible feedback mechanism detected in the project'}
- Technologies: ${projectInfo.technologies.join(', ')}

Generate a user feedback strategy guide for a Vibe Coder. Include:
- The current score and identified gaps.
- A step-by-step guide to add a feedback system.
- A code snippet for implementation.
- Best practices for user feedback.
- Recommended tools.
Keep the tone practical and encouraging for rapid implementation.

Format your response as JSON with these fields: gaps (string), guide (string), codeSnippet (string), bestPractices (array), tools (array)

Example format:
{
  "gaps": "No visible feedback mechanism detected in the project.",
  "guide": "1. Create a feedback form component in React using React Hook Form. 2. Add a POST endpoint in index.js to handle form submissions. 3. Store responses in Firebase for analysis. 4. Display the form on a dedicated /feedback page.",
  "codeSnippet": "// FeedbackForm.js\\nimport { useForm } from 'react-hook-form';\\nexport default function FeedbackForm() {\\n  const { register, handleSubmit } = useForm();\\n  const onSubmit = async (data) => {\\n    await fetch('/feedback', { method: 'POST', body: JSON.stringify(data) });\\n  };\\n  return (\\n    <form onSubmit={handleSubmit(onSubmit)}>\\n      <input {...register('message')} placeholder=\\"Your feedback\\" />\\n      <button type=\\"submit\\">Submit</button>\\n    </form>\\n  );\\n}",
  "bestPractices": [
    "Keep surveys short (<5 questions) to maximize responses",
    "Offer incentives (e.g., early access) for feedback"
  ],
  "tools": [
    "Hotjar for heatmaps",
    "Google Analytics for user behavior tracking"
  ]
}
  `;

  try {
    const result = await makeOpenAIJSONRequest(prompt);
    
    return {
      currentScore,
      gaps: result.gaps,
      guide: result.guide,
      codeSnippet: result.codeSnippet,
      bestPractices: result.bestPractices,
      tools: result.tools
    };
  } catch (error) {
    console.error("Error generating feedback strategy:", error);
    return {
      currentScore,
      gaps: "Gaps in feedback collection mechanisms need to be addressed",
      guide: "Implement user surveys and feedback forms in your application",
      codeSnippet: "// Example feedback form component",
      bestPractices: ["Keep forms short", "Ask specific questions", "Follow up with users"],
      tools: ["Google Forms", "Typeform", "Hotjar", "UserTesting.com"]
    };
  }
}

/**
 * Generate deployment checklist section
 */
async function generateDeploymentChecklist(projectInfo: any, scoreData: any): Promise<{
  currentScore: number;
  gaps: string;
  hosting: string;
  performance: string;
  security: string;
  monitoring: string;
  timeline: string;
}> {
  // Find the launch readiness criterion
  const launchCriterion = scoreData.criterionScores.find((c: any) =>
    c.name.toLowerCase().includes('launch') || c.name.toLowerCase().includes('readiness'));
  
  const currentScore = launchCriterion ? launchCriterion.score : 0;
  
  const prompt = `
Given the project analysis data for Launch Readiness:
- Score: ${currentScore}/5
- Gaps: ${launchCriterion ? launchCriterion.feedback : 'Ready for closed beta but not public release due to untested features'}
- Technologies: ${projectInfo.technologies.join(', ')}

Generate a deployment and launch checklist for a Vibe Coder. Include:
- The current score and identified gaps.
- A checklist for launch preparation.
- A suggested timeline.
Keep the tone actionable and focused on rapid deployment.

Format your response as JSON with these fields: gaps (string), hosting (string), performance (string), security (string), monitoring (string), timeline (string)

Example format:
{
  "gaps": "Ready for closed beta but not public release due to untested features.",
  "hosting": "Deploy on Vercel for Next.js projects.",
  "performance": "Minify CSS/JS, enable compression.",
  "security": "Add helmet.js for HTTP headers.",
  "monitoring": "Integrate Sentry for error tracking.",
  "timeline": "Week 1: Fix bugs and deploy to Vercel. Week 2: Beta test with 10 users and gather feedback."
}
  `;

  try {
    const result = await makeOpenAIJSONRequest(prompt);
    
    return {
      currentScore,
      gaps: result.gaps,
      hosting: result.hosting,
      performance: result.performance,
      security: result.security,
      monitoring: result.monitoring,
      timeline: result.timeline
    };
  } catch (error) {
    console.error("Error generating deployment checklist:", error);
    return {
      currentScore,
      gaps: "Deployment readiness needs improvement",
      hosting: "Consider using a cloud platform suitable for your stack",
      performance: "Optimize assets and implement caching",
      security: "Implement proper authentication and data validation",
      monitoring: "Set up error tracking and performance monitoring",
      timeline: "2-3 weeks for preparation and deployment"
    };
  }
}

/**
 * Generate testing recommendations section
 */
async function generateTestingRecommendations(projectInfo: any, scoreData: any): Promise<{
  currentScore: number;
  gaps: string[];
  frameworks: string[];
  sampleTests: string;
  userTestingPlan: string;
}> {
  // Find the testing criterion
  const testingCriterion = scoreData.criterionScores.find((c: any) =>
    c.name.toLowerCase().includes('test'));
  
  const currentScore = testingCriterion ? testingCriterion.score : 0;
  
  const prompt = `
Given the project analysis data for Testing:
- Score: ${currentScore}/5
- Gaps: ${testingCriterion ? testingCriterion.feedback : 'No clear evidence of comprehensive testing strategy or user testing'}
- Files: ${projectInfo.files ? projectInfo.files.slice(0, 5).join(', ') : 'Various project files'}

Generate testing and stability recommendations for a Vibe Coder. Include:
- The current score and identified gaps.
- Specific testing gaps (e.g., missing tests for a file).
- Recommended frameworks.
- Sample test cases.
- A user testing plan.
Keep the tone technical and actionable.

Format your response as JSON with these fields: gaps (array), frameworks (array), sampleTests (string), userTestingPlan (string)

Example format:
{
  "gaps": [
    "No unit tests for auth.js",
    "No end-to-end tests detected"
  ],
  "frameworks": [
    "Jest for unit tests",
    "Cypress for E2E tests"
  ],
  "sampleTests": "// auth.test.js\\ntest('user login', () => {\\n  const user = { email: '<EMAIL>', password: 'password' };\\n  expect(login(user)).toBe(true);\\n});",
  "userTestingPlan": "Recruit 5 beta testers via Discord. Ask them to test the login flow and provide feedback."
}
  `;

  try {
    const result = await makeOpenAIJSONRequest(prompt);

    return {
      currentScore,
      gaps: result.gaps,
      frameworks: result.frameworks,
      sampleTests: result.sampleTests,
      userTestingPlan: result.userTestingPlan
    };
  } catch (error) {
    console.error("Error generating testing recommendations:", error);
    return {
      currentScore,
      gaps: ["Lack of unit tests", "No end-to-end testing", "Missing edge case coverage"],
      frameworks: ["Jest", "React Testing Library", "Cypress"],
      sampleTests: "// Example test code\ntest('user login', () => {\n  expect(login(user)).toBeTruthy();\n});",
      userTestingPlan: "Recruit 5-10 test users and prepare specific testing scenarios"
    };
  }
}

/**
 * Generate documentation plan section
 */
async function generateDocumentationPlan(projectInfo: any, scoreData: any): Promise<{
  currentScore: number;
  gaps: string[];
  readmeTemplate: string;
  tools: string[];
  examples: string;
}> {
  // Find the documentation criterion
  const docsCriterion = scoreData.criterionScores.find((c: any) =>
    c.name.toLowerCase().includes('doc'));
  
  const currentScore = docsCriterion ? docsCriterion.score : 0;
  
  const prompt = `
Given the project analysis data for Documentation:
- Score: ${currentScore}/5
- Gaps: ${docsCriterion ? docsCriterion.feedback : 'Limited mention of documentation; no user guide'}
- Files: ${projectInfo.files ? projectInfo.files.slice(0, 5).join(', ') : 'Various project files'}

Generate a documentation enhancement plan for a Vibe Coder. Include:
- The current score and identified gaps.
- Specific documentation gaps.
- A README template.
- Recommended tools.
- An example action.
Keep the tone practical and focused on quick wins.

Format your response as JSON with these fields: gaps (array), readmeTemplate (string), tools (array), examples (string)

Example format:
{
  "gaps": [
    "No user guide in the project",
    "README lacks setup instructions"
  ],
  "readmeTemplate": "# Project Name\\n\\n## Installation\\nSteps to install dependencies.\\n\\n## Usage\\nHow to run the project.\\n\\n## Contributing\\nGuidelines for contributors.",
  "tools": [
    "Use Docusaurus for a user-friendly docs site"
  ],
  "examples": "Add a docs/getting-started.md file with setup steps."
}
  `;

  try {
    const result = await makeOpenAIJSONRequest(prompt);

    return {
      currentScore,
      gaps: result.gaps,
      readmeTemplate: result.readmeTemplate,
      tools: result.tools,
      examples: result.examples
    };
  } catch (error) {
    console.error("Error generating documentation plan:", error);
    return {
      currentScore,
      gaps: ["Missing setup instructions", "No API documentation", "Lacks user guide"],
      readmeTemplate: "# Project Name\n\n## Description\n\n## Installation\n\n## Usage\n\n## API Documentation\n\n## Contributing\n\n## License",
      tools: ["Docusaurus", "GitHub Wiki", "JSDoc", "Swagger"],
      examples: "# API Endpoint\n\n`GET /api/users`\n\nReturns a list of users.\n\n## Parameters\n\n- `limit`: Maximum number of users to return"
    };
  }
}

/**
 * Generate benchmarking section
 */
async function generateBenchmarking(_projectInfo: any, scoreData: any): Promise<{
  score: number;
  averageScore: number;
  strengths: string[];
  weaknesses: string[];
}> {
  const totalScore = scoreData.total;
  // This would normally be fetched from a database of scores, but we'll simulate for now
  const averageScore = 27;
  
  const prompt = `
Given the project analysis data:
- Score: ${totalScore}/40
- Criteria Scores: ${scoreData.criterionScores.map((c: any) => `${c.name}: ${c.score}/5`).join(', ')}
- Industry Average: ${averageScore}/40

Generate a competitive benchmarking section for a Vibe Coder. Include:
- A comparison of the project's score to the industry average.
- Areas where the project excels.
- Areas to improve to match competitors.
Keep the tone motivational and data-driven.

Format your response as JSON with these fields: strengths (array), weaknesses (array)

Example format:
{
  "strengths": [
    "UI consistency is better than 70% of peers"
  ],
  "weaknesses": [
    "Top MVPs have 80% test coverage; aim for 60% to start"
  ]
}
  `;

  try {
    const result = await makeOpenAIJSONRequest(prompt);

    return {
      score: totalScore,
      averageScore,
      strengths: result.strengths,
      weaknesses: result.weaknesses
    };
  } catch (error) {
    console.error("Error generating benchmarking:", error);
    return {
      score: totalScore,
      averageScore,
      strengths: scoreData.strengths.slice(0, 3),
      weaknesses: scoreData.weaknesses.slice(0, 3)
    };
  }
}

/**
 * Generate resource library section
 */
async function generateResourceLibrary(projectInfo: any, scoreData: any): Promise<{
  tutorials: string[];
  tools: string[];
  communities: string[];
  templates: string[];
}> {
  const prompt = `
Given the project analysis data:
- Gaps: ${scoreData.weaknesses.join(', ')}
- Technologies: ${projectInfo.technologies.join(', ')}

Generate a resource library for a Vibe Coder. Include:
- Tutorials relevant to identified gaps.
- Tools to address gaps.
- Communities for support.
- Templates for quick implementation.
Keep the tone helpful and resourceful.

Format your response as JSON with these fields: tutorials (array), tools (array), communities (array), templates (array)

Example format:
{
  "tutorials": [
    "How to Add Feedback Forms in React (link)"
  ],
  "tools": [
    "Free testing tools: Jest, Cypress"
  ],
  "communities": [
    "Join the Vibe Coding Discord for peer feedback"
  ],
  "templates": [
    "Download a sample README template (link)"
  ]
}
  `;

  try {
    const result = await makeOpenAIJSONRequest(prompt);

    return {
      tutorials: result.tutorials,
      tools: result.tools,
      communities: result.communities,
      templates: result.templates
    };
  } catch (error) {
    console.error("Error generating resource library:", error);
    return {
      tutorials: ["Getting Started with React", "State Management Best Practices"],
      tools: ["React DevTools", "Lighthouse", "ESLint"],
      communities: ["Stack Overflow", "React Community Discord", "DEV.to"],
      templates: ["Create React App", "Next.js Starter Templates"]
    };
  }
}

/**
 * Generate roadmap section
 */
async function generateRoadmap(_projectInfo: any, scoreData: any): Promise<{
  currentLevel: string;
  targetLevel: string;
  steps: Array<{
    title: string;
    priority: 'High' | 'Medium' | 'Low';
    estimatedTime: string;
  }>;
  timeline: string;
}> {
  const currentScore = scoreData.total;
  const currentLevel = scoreData.category;
  let targetLevel = '';
  
  // Determine target level based on current score
  if (currentScore < 15) {
    targetLevel = "Basic MVP (15-25 points)";
  } else if (currentScore < 25) {
    targetLevel = "Solid MVP (26-35 points)";
  } else if (currentScore < 35) {
    targetLevel = "Polished MVP (36-40 points)";
  } else {
    targetLevel = "Production Ready";
  }
  
  const prompt = `
Given the project analysis data:
- Score: ${currentScore}/40
- Weaknesses: ${scoreData.weaknesses.join(', ')}
- Recommendations: ${scoreData.recommendations.map((r: any) => r.title).join(', ')}

Generate a personalized roadmap for a Vibe Coder. Include:
- The current and target MVP readiness level.
- Actionable steps with priorities and estimated times.
- A suggested timeline.
Keep the tone actionable and encouraging.

Format your response as JSON with these fields: steps (array of objects with title, priority, estimatedTime), timeline (string)

Example format:
{
  "steps": [
    {
      "title": "Add feedback form",
      "priority": "High",
      "estimatedTime": "2 hours"
    },
    {
      "title": "Write user guide",
      "priority": "Medium",
      "estimatedTime": "4 hours"
    },
    {
      "title": "Optimize images",
      "priority": "Low",
      "estimatedTime": "3 hours"
    }
  ],
  "timeline": "Reach Solid MVP in 2 weeks with 10 hours of work."
}
  `;

  try {
    const result = await makeOpenAIJSONRequest(prompt);

    return {
      currentLevel,
      targetLevel,
      steps: result.steps,
      timeline: result.timeline
    };
  } catch (error) {
    console.error("Error generating roadmap:", error);
    return {
      currentLevel,
      targetLevel,
      steps: scoreData.recommendations.map((r: any) => ({
        title: r.title,
        priority: r.priority,
        estimatedTime: "1-2 days"
      })),
      timeline: "2-4 weeks to reach target level with consistent effort"
    };
  }
}

/**
 * Generate visualizations section
 */
async function generateVisualizations(_projectInfo: any, scoreData: any): Promise<{
  radarChartData: any;
  codeQualityMetrics: Record<string, string | number>;
  performanceMetrics?: Record<string, string | number>; // Optional - can't measure from static code
  comparisonData: any;
}> {
  const prompt = `
Given the project analysis data:
- Scores: ${scoreData.criterionScores.map((c: any) => `${c.name}: ${c.score}/5`).join(', ')}
- Code Metrics: ${scoreData.codeMetrics || 'Standard metrics detected'}
// - Performance Metrics: ${scoreData.performanceMetrics || 'Load time analysis pending'} // Commented out - can't measure from static code
- Industry Average: 27/40

Generate visualizations and metrics for a Vibe Coder. Include:
- A radar chart of scores.
- Code quality metrics (e.g., coverage, complexity).
// - Performance metrics (e.g., load time). // Commented out - can't measure from static code
- A comparison chart vs. industry average.
Keep the tone data-driven and visual.

Format your response as JSON with these fields: codeQualityMetrics (object)
// performanceMetrics (object) // Commented out - can't measure from static code

Example format:
{
  "codeQualityMetrics": {
    "Code Coverage": "40%",
    "Cyclomatic Complexity": 5
  }
}
  `;

  try {
    const result = await makeOpenAIJSONRequest(prompt);

    // We already have radar chart data in the scoreData
    const radarChartData = scoreData.radarData;

    // Create comparison data
    const comparisonData = {
      labels: ["Your Score", "Average Score", "Top Score"],
      datasets: [{
        label: "MVP Readiness",
        data: [scoreData.total, 27, 38],
        backgroundColor: ["rgba(75, 192, 192, 0.6)", "rgba(54, 162, 235, 0.6)", "rgba(255, 159, 64, 0.6)"]
      }]
    };

    return {
      radarChartData,
      codeQualityMetrics: result.codeQualityMetrics || {
        "Code Coverage": "40%",
        "Cyclomatic Complexity": 5,
        "Maintainability Index": 65,
        "Technical Debt Ratio": "22%"
      },
      // performanceMetrics: result.performanceMetrics || {
      //   "Page Load Time": "3.2s",
      //   "First Contentful Paint": "1.8s",
      //   "Total Bundle Size": "1.2MB",
      //   "API Response Time": "450ms"
      // }, // Commented out - can't measure performance from static code
      comparisonData
    };
  } catch (error) {
    console.error("Error generating visualizations:", error);

    // Fallback data
    const radarChartData = scoreData.radarData;
    const codeQualityMetrics = {
      "Code Coverage": "40%",
      "Cyclomatic Complexity": 5,
      "Maintainability Index": 65,
      "Technical Debt Ratio": "22%"
    };
    // const performanceMetrics = {
    //   "Page Load Time": "3.2s",
    //   "First Contentful Paint": "1.8s",
    //   "Total Bundle Size": "1.2MB",
    //   "API Response Time": "450ms"
    // }; // Commented out - can't measure performance from static code
    const comparisonData = {
      labels: ["Your Score", "Average Score", "Top Score"],
      datasets: [{
        label: "MVP Readiness",
        data: [scoreData.total, 27, 38],
        backgroundColor: ["rgba(75, 192, 192, 0.6)", "rgba(54, 162, 235, 0.6)", "rgba(255, 159, 64, 0.6)"]
      }]
    };

    return {
      radarChartData,
      codeQualityMetrics,
      // performanceMetrics, // Commented out - can't measure performance from static code
      comparisonData
    };
  }
}

/**
 * Generate a full PDF report using the modular PDF generator
 */
export async function generatePDF(fullReport: FullReportData, analysisId: number, projectName: string = "Unknown Project"): Promise<Buffer | null> {
  return await generatePDFBuffer(fullReport, analysisId, projectName);
}

/**
 * Generate and save PDF, returning the download URL
 */
export async function generateAndSaveReportPDF(fullReport: FullReportData, analysisId: number, projectName: string = "Unknown Project"): Promise<string | null> {
  return await generateAndSavePDF(fullReport, analysisId, projectName);
}