import OpenAI from "openai";

// The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY || "sk-demo-key" });

/**
 * Make a JSON-formatted request to OpenAI
 */
export async function makeOpenAIJSONRequest(prompt: string): Promise<any> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [{ role: "user", content: prompt }],
      response_format: { type: "json_object" }
    });

    return JSON.parse(response.choices[0].message.content || '{}');
  } catch (error) {
    console.error("Error making OpenAI JSON request:", error);
    return {};
  }
}

/**
 * Make a text request to OpenAI
 */
export async function makeOpenAITextRequest(prompt: string): Promise<string> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [{ role: "user", content: prompt }]
    });

    return response.choices[0].message.content || '';
  } catch (error) {
    console.error("Error making OpenAI text request:", error);
    return '';
  }
}

/**
 * Find a criterion by name in score data
 */
export function findCriterion(scoreData: any, searchTerms: string[]): any {
  return scoreData.criterionScores?.find((c: any) => 
    searchTerms.some(term => c.name.toLowerCase().includes(term.toLowerCase()))
  );
}

/**
 * Get score class for styling
 */
export function getScoreClass(score: number): string {
  if (score <= 2) return 'low';
  if (score <= 3) return 'medium';
  return 'high';
}

/**
 * Create a standardized prompt for report sections
 */
export function createSectionPrompt(
  sectionType: string,
  projectInfo: any,
  scoreData: any,
  specificData?: any
): string {
  const baseInfo = `
Project: ${projectInfo.name || 'Unknown Project'}
Technologies: ${projectInfo.technologies?.join(', ') || 'Not specified'}
Files: ${projectInfo.files ? projectInfo.files.slice(0, 5).join(', ') : 'Various project files'}
`;

  switch (sectionType) {
    case 'executive-summary':
      return `
Given the project analysis data: 
- Project Name: ${projectInfo.name} 
- Project Type: ${projectInfo.type || 'Web Application'} 
- Score: ${scoreData.total}/40 
- MVP Readiness Level: ${scoreData.category} 
- Strengths: ${scoreData.strengths.join(', ')} 
- Weaknesses: ${scoreData.weaknesses.join(', ')} 

Generate an executive summary for a Vibe Coder. Include: 
- The project name, type, and score. 
- The MVP readiness level with a brief explanation. 
- The top 3 strengths and top 3 areas for improvement, formatted as bullet points. 
Keep the tone concise, professional, and encouraging for rapid iteration.

Format the response as follows:
**Executive Summary** 
**Project**: [Name], [Type], [Score]/40   
**MVP Readiness**: [Level] - [Brief explanation]   
**Top 3 Strengths**: 
- [Strength 1] 
- [Strength 2] 
- [Strength 3] 
**Top 3 Areas for Improvement**: 
- [Area 1] 
- [Area 2] 
- [Area 3]
      `;

    case 'detailed-scores':
      return `
Given the project analysis data for criterion ${specificData.name}: 
- Score: ${specificData.score}/5 
- Explanation: ${specificData.feedback || specificData.description} 
- Files Analyzed: ${projectInfo.files ? projectInfo.files.slice(0, 5).join(', ') : 'Various project files'} 
- Code Issues: ${specificData.issues || 'See analysis details'} 
- Technologies: ${projectInfo.technologies.join(', ')} 

Generate a detailed breakdown for a Vibe Coder. Include: 
- The criterion name, score, and explanation. 
- Specific examples from the project (e.g., file names, code snippets). 
- A code snippet for improvement if applicable. 
- Actionable suggestions for improvement. 
Keep the tone technical, actionable, and focused on rapid iteration.

Format your response as JSON with these fields: explanation, examples, codeSnippet (optional), suggestions
      `;

    case 'tech-stack':
      return `
Given the project analysis data: 
- Technologies: ${projectInfo.technologies.join(', ')} 
- Performance Metrics: ${scoreData.performanceMetrics || 'Load time analysis pending'} 
- Dependencies: ${projectInfo.dependencies || 'Standard dependencies detected'} 

Generate a technology stack analysis for a Vibe Coder. Include: 
- A list of detected technologies. 
- Pros and cons of the stack for rapid development. 
- Recommendations for optimization. 
- Alternative tools if applicable. 
Keep the tone technical and focused on scalability and speed.

Format your response as JSON with these fields: pros (array), cons (array), recommendations (array), alternatives (array)
      `;

    default:
      return baseInfo;
  }
}
