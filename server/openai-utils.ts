import OpenAI from "openai";

// Enhanced model configuration for different use cases
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY || "sk-demo-key" });

// Model selection based on task complexity and requirements
export const MODEL_CONFIG = {
  // For complex reasoning, architectural analysis, and strategic recommendations
  REASONING: "o1-mini", // Cost-effective reasoning model
  // For general analysis and quick responses
  GENERAL: "gpt-4o",
  // For detailed code analysis (when available)
  ADVANCED_REASONING: "o1-preview"
} as const;

export type ModelType = keyof typeof MODEL_CONFIG;

/**
 * Enhanced OpenAI request with model selection and advanced parameters
 */
export async function makeOpenAIRequest(
  prompt: string,
  options: {
    model?: ModelType;
    responseFormat?: 'json' | 'text';
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
  } = {}
): Promise<any> {
  const {
    model = 'GENERAL',
    responseFormat = 'text',
    temperature = 0.7,
    maxTokens,
    systemPrompt
  } = options;

  try {
    const messages: any[] = [];

    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }

    messages.push({ role: "user", content: prompt });

    const requestConfig: any = {
      model: MODEL_CONFIG[model],
      messages,
      temperature,
    };

    // Add response format for JSON requests
    if (responseFormat === 'json') {
      requestConfig.response_format = { type: "json_object" };
    }

    // Add max tokens if specified
    if (maxTokens) {
      requestConfig.max_tokens = maxTokens;
    }

    const response = await openai.chat.completions.create(requestConfig);

    const content = response.choices[0].message.content || '';

    return responseFormat === 'json' ? JSON.parse(content || '{}') : content;
  } catch (error) {
    console.error(`Error making OpenAI ${model} request:`, error);
    return responseFormat === 'json' ? {} : '';
  }
}

/**
 * Make a JSON-formatted request to OpenAI (backward compatibility)
 */
export async function makeOpenAIJSONRequest(prompt: string, model: ModelType = 'GENERAL'): Promise<any> {
  return makeOpenAIRequest(prompt, { model, responseFormat: 'json' });
}

/**
 * Make a text request to OpenAI (backward compatibility)
 */
export async function makeOpenAITextRequest(prompt: string, model: ModelType = 'GENERAL'): Promise<string> {
  return makeOpenAIRequest(prompt, { model, responseFormat: 'text' });
}

/**
 * Find a criterion by name in score data
 */
export function findCriterion(scoreData: any, searchTerms: string[]): any {
  return scoreData.criterionScores?.find((c: any) => 
    searchTerms.some(term => c.name.toLowerCase().includes(term.toLowerCase()))
  );
}

/**
 * Get score class for styling
 */
export function getScoreClass(score: number): string {
  if (score <= 2) return 'low';
  if (score <= 3) return 'medium';
  return 'high';
}

/**
 * Enhanced prompting strategies for comprehensive codebase analysis
 */
export const ENHANCED_PROMPTS = {
  SYSTEM_PROMPTS: {
    ARCHITECTURAL_ANALYST: `You are an expert software architect and MVP consultant. Your role is to:
- Analyze codebases with deep technical insight
- Identify architectural patterns, anti-patterns, and design decisions
- Provide strategic recommendations for MVP development
- Focus on scalability, maintainability, and rapid iteration
- Consider both technical debt and business value`,

    CODE_REVIEWER: `You are a senior code reviewer specializing in MVP development. Your expertise includes:
- Code quality assessment and best practices
- Security vulnerability identification
- Performance optimization opportunities
- Testing strategy recommendations
- Documentation and maintainability analysis`,

    BUSINESS_STRATEGIST: `You are a product strategist focused on MVP success. You excel at:
- Market readiness assessment
- User experience evaluation
- Feature prioritization for MVP launch
- Competitive analysis and positioning
- Go-to-market strategy recommendations`
  },

  COMPREHENSIVE_ANALYSIS: {
    CODEBASE_STRUCTURE: `Analyze the complete codebase structure and provide insights on:
1. **Architecture Patterns**: Identify design patterns, architectural style (MVC, microservices, etc.)
2. **Code Organization**: Assess folder structure, separation of concerns, modularity
3. **Dependencies**: Evaluate dependency management, potential security risks, version conflicts
4. **Scalability**: Identify bottlenecks and scalability concerns
5. **Technical Debt**: Highlight areas needing refactoring or improvement
6. **Best Practices**: Assess adherence to language/framework conventions`,

    BUSINESS_READINESS: `Evaluate MVP business readiness across these dimensions:
1. **Core Value Proposition**: Does the code deliver the promised core functionality?
2. **User Journey**: Are critical user paths implemented and functional?
3. **Market Fit Indicators**: Evidence of user feedback mechanisms, analytics, A/B testing
4. **Launch Readiness**: Deployment configuration, monitoring, error handling
5. **Competitive Advantage**: Unique features or technical implementations
6. **Growth Potential**: Extensibility and feature expansion capabilities`,

    SECURITY_ASSESSMENT: `Conduct a comprehensive security analysis:
1. **Authentication & Authorization**: Implementation quality and security
2. **Data Protection**: Encryption, sensitive data handling, GDPR compliance
3. **Input Validation**: SQL injection, XSS, CSRF protection
4. **API Security**: Rate limiting, authentication, data exposure
5. **Infrastructure**: Environment variables, secrets management
6. **Third-party Dependencies**: Known vulnerabilities, update status`
  }
};

/**
 * Create enhanced prompts with comprehensive codebase context
 */
export function createEnhancedPrompt(
  analysisType: 'ARCHITECTURAL' | 'BUSINESS' | 'SECURITY' | 'COMPREHENSIVE',
  projectInfo: any,
  codebaseContext: {
    fileStructure?: string;
    codeSnippets?: { [key: string]: string };
    dependencies?: string[];
    metrics?: any;
  } = {}
): { systemPrompt: string; userPrompt: string } {
  const { fileStructure, codeSnippets, dependencies, metrics } = codebaseContext;

  let systemPrompt = '';
  let analysisPrompt = '';

  switch (analysisType) {
    case 'ARCHITECTURAL':
      systemPrompt = ENHANCED_PROMPTS.SYSTEM_PROMPTS.ARCHITECTURAL_ANALYST;
      analysisPrompt = ENHANCED_PROMPTS.COMPREHENSIVE_ANALYSIS.CODEBASE_STRUCTURE;
      break;
    case 'BUSINESS':
      systemPrompt = ENHANCED_PROMPTS.SYSTEM_PROMPTS.BUSINESS_STRATEGIST;
      analysisPrompt = ENHANCED_PROMPTS.COMPREHENSIVE_ANALYSIS.BUSINESS_READINESS;
      break;
    case 'SECURITY':
      systemPrompt = ENHANCED_PROMPTS.SYSTEM_PROMPTS.CODE_REVIEWER;
      analysisPrompt = ENHANCED_PROMPTS.COMPREHENSIVE_ANALYSIS.SECURITY_ASSESSMENT;
      break;
    case 'COMPREHENSIVE':
      systemPrompt = `${ENHANCED_PROMPTS.SYSTEM_PROMPTS.ARCHITECTURAL_ANALYST}\n\n${ENHANCED_PROMPTS.SYSTEM_PROMPTS.BUSINESS_STRATEGIST}`;
      analysisPrompt = `${ENHANCED_PROMPTS.COMPREHENSIVE_ANALYSIS.CODEBASE_STRUCTURE}\n\n${ENHANCED_PROMPTS.COMPREHENSIVE_ANALYSIS.BUSINESS_READINESS}`;
      break;
  }

  const userPrompt = `
## Project Analysis Request

**Project Information:**
- Name: ${projectInfo.name || 'Unknown Project'}
- Type: ${projectInfo.type || 'Web Application'}
- Technologies: ${projectInfo.technologies?.join(', ') || 'Not specified'}
- Description: ${projectInfo.description || 'No description provided'}

${fileStructure ? `**File Structure:**
\`\`\`
${fileStructure}
\`\`\`` : ''}

${dependencies ? `**Dependencies:**
${dependencies.join(', ')}` : ''}

${codeSnippets ? `**Key Code Snippets:**
${Object.entries(codeSnippets).map(([file, code]) =>
  `**${file}:**
\`\`\`
${code}
\`\`\``
).join('\n\n')}` : ''}

${metrics ? `**Code Metrics:**
${JSON.stringify(metrics, null, 2)}` : ''}

## Analysis Requirements:
${analysisPrompt}

**Output Format:** Provide a comprehensive JSON response with detailed analysis, specific recommendations, and actionable insights. Include code examples where relevant.
`;

  return { systemPrompt, userPrompt };
}

/**
 * Create a standardized prompt for report sections (backward compatibility)
 */
export function createSectionPrompt(
  sectionType: string,
  projectInfo: any,
  scoreData: any,
  specificData?: any
): string {
  const baseInfo = `
Project: ${projectInfo.name || 'Unknown Project'}
Technologies: ${projectInfo.technologies?.join(', ') || 'Not specified'}
Files: ${projectInfo.files ? projectInfo.files.slice(0, 5).join(', ') : 'Various project files'}
`;

  switch (sectionType) {
    case 'executive-summary':
      return `
Given the project analysis data:
- Project Name: ${projectInfo.name}
- Project Type: ${projectInfo.type || 'Web Application'}
- Score: ${scoreData.total}/40
- MVP Readiness Level: ${scoreData.category}
- Strengths: ${scoreData.strengths.join(', ')}
- Weaknesses: ${scoreData.weaknesses.join(', ')}

Generate an executive summary for a Vibe Coder. Include:
- The project name, type, and score.
- The MVP readiness level with a brief explanation.
- The top 3 strengths and top 3 areas for improvement, formatted as bullet points.
Keep the tone concise, professional, and encouraging for rapid iteration.

Format the response as follows:
**Executive Summary**
**Project**: [Name], [Type], [Score]/40
**MVP Readiness**: [Level] - [Brief explanation]
**Top 3 Strengths**:
- [Strength 1]
- [Strength 2]
- [Strength 3]
**Top 3 Areas for Improvement**:
- [Area 1]
- [Area 2]
- [Area 3]
      `;

    case 'detailed-scores':
      return `
Given the project analysis data for criterion ${specificData.name}:
- Score: ${specificData.score}/5
- Explanation: ${specificData.feedback || specificData.description}
- Files Analyzed: ${projectInfo.files ? projectInfo.files.slice(0, 5).join(', ') : 'Various project files'}
- Code Issues: ${specificData.issues || 'See analysis details'}
- Technologies: ${projectInfo.technologies.join(', ')}

Generate a detailed breakdown for a Vibe Coder. Include:
- The criterion name, score, and explanation.
- Specific examples from the project (e.g., file names, code snippets).
- A code snippet for improvement if applicable.
- Actionable suggestions for improvement.
Keep the tone technical, actionable, and focused on rapid iteration.

Format your response as JSON with these fields: explanation, examples, codeSnippet (optional), suggestions
      `;

    case 'tech-stack':
      return `
Given the project analysis data:
- Technologies: ${projectInfo.technologies.join(', ')}
- Performance Metrics: ${scoreData.performanceMetrics || 'Load time analysis pending'}
- Dependencies: ${projectInfo.dependencies || 'Standard dependencies detected'}

Generate a technology stack analysis for a Vibe Coder. Include:
- A list of detected technologies.
- Pros and cons of the stack for rapid development.
- Recommendations for optimization.
- Alternative tools if applicable.
Keep the tone technical and focused on scalability and speed.

Format your response as JSON with these fields: pros (array), cons (array), recommendations (array), alternatives (array)
      `;

    default:
      return baseInfo;
  }
}
