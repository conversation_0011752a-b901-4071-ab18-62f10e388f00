import OpenAI from "openai";

// The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY || "sk-demo-key" });

export interface CodeAnalysisResult {
  projectName: string;
  technologiesDetected: string[];
  criteriaScores: {
    coreFunctionality: number;
    userCentricDesign: number;
    technicalStability: number;
    feedbackCollection: number;
    minimumViability: number;
    launchReadiness: number;
    documentation: number;
    testing: number;
  };
  strengths: string[];
  weaknesses: string[];
  recommendations: {
    title: string;
    description: string;
    priority: "High" | "Medium" | "Low";
  }[];
  feedback: {
    coreFunctionality: string;
    userCentricDesign: string;
    technicalStability: string;
    feedbackCollection: string;
    minimumViability: string;
    launchReadiness: string;
    documentation: string;
    testing: string;
  };
}

export async function analyzeProjectCode(
  fileContents: { [path: string]: string },
  projectType: string,
  description: string
): Promise<CodeAnalysisResult> {
  try {
    // Prepare the data for OpenAI - we need to limit the amount of code we send
    const fileInfos = Object.entries(fileContents).map(([path, content]) => {
      // Limit content length to avoid token limits
      const limitedContent = content.length > 5000 
        ? content.substring(0, 5000) + '... [content truncated]' 
        : content;
      
      return {
        path,
        content: limitedContent,
        extension: path.split('.').pop()
      };
    });
    
    // Send only the first X files to stay within token limits
    const MAX_FILES = 15;
    const limitedFiles = fileInfos.slice(0, MAX_FILES);
    
    const fileSummary = limitedFiles.map(file => 
      `File: ${file.path}\nExtension: ${file.extension}\nContent: ${file.content.substring(0, 200)}...`
    ).join('\n\n');
    
    const prompt = `
    Analyze this software project as an MVP readiness assessor. Score it on 8 criteria (0-5 scale):
    
    Project Type: ${projectType}
    Project Description: ${description || "No description provided"}
    
    Files Overview: ${limitedFiles.length} files analyzed${limitedFiles.length < fileInfos.length 
      ? ` (truncated from ${fileInfos.length} total files)` 
      : ''}
    
    ${fileSummary}
    
    Score the project on these criteria (0-5 scale):
    1. Core Functionality: Are essential features implemented and working?
    2. User-Centric Design: Is the UI intuitive and user-friendly?
    3. Technical Stability: Is the code reliable with minimal bugs?
    4. Feedback Collection: Are mechanisms in place to gather user input?
    5. Minimum Viability: Does it include only necessary features?
    6. Launch Readiness: Is it ready for a limited or public release?
    7. Documentation: Are there clear guides for users/developers?
    8. Testing: Has it been tested, ideally with users?
    
    Provide detailed feedback for each criterion, identify strengths and weaknesses, and recommend 
    up to 3 high-priority improvements. Try to determine the project name from the code.
    
    Respond with JSON in this exact format:
    {
      "projectName": "string",
      "technologiesDetected": ["string"],
      "criteriaScores": {
        "coreFunctionality": number,
        "userCentricDesign": number,
        "technicalStability": number,
        "feedbackCollection": number,
        "minimumViability": number,
        "launchReadiness": number,
        "documentation": number,
        "testing": number
      },
      "strengths": ["string"],
      "weaknesses": ["string"],
      "recommendations": [
        {
          "title": "string",
          "description": "string",
          "priority": "High|Medium|Low"
        }
      ],
      "feedback": {
        "coreFunctionality": "string",
        "userCentricDesign": "string",
        "technicalStability": "string",
        "feedbackCollection": "string",
        "minimumViability": "string",
        "launchReadiness": "string",
        "documentation": "string",
        "testing": "string"
      }
    }
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [{ role: "user", content: prompt }],
      response_format: { type: "json_object" }
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("No content received from OpenAI");
    }
    const result = JSON.parse(content) as CodeAnalysisResult;
    
    return result;
  } catch (error) {
    console.error("OpenAI API error:", error);
    
    // Return a fallback result if the API fails
    return {
      projectName: "Unknown Project",
      technologiesDetected: ["Unknown"],
      criteriaScores: {
        coreFunctionality: 2,
        userCentricDesign: 2,
        technicalStability: 2,
        feedbackCollection: 2,
        minimumViability: 2,
        launchReadiness: 2,
        documentation: 2,
        testing: 2
      },
      strengths: ["Unable to determine strengths due to analysis error"],
      weaknesses: ["Analysis failed, please try again with a smaller project"],
      recommendations: [
        {
          title: "Retry with fewer files",
          description: "The analysis failed, possibly due to project size or complexity. Try uploading a subset of your project files.",
          priority: "High"
        }
      ],
      feedback: {
        coreFunctionality: "Analysis failed",
        userCentricDesign: "Analysis failed",
        technicalStability: "Analysis failed",
        feedbackCollection: "Analysis failed",
        minimumViability: "Analysis failed",
        launchReadiness: "Analysis failed",
        documentation: "Analysis failed",
        testing: "Analysis failed"
      }
    };
  }
}
