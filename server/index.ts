import dotenv from "dotenv";
dotenv.config();

import express, { type Request, Response, NextFunction } from "express";
import session from 'express-session';
import ConnectPgSimple from 'connect-pg-simple';
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import authRoutes from './auth/google-auth';

const app = express();
app.set('trust proxy', 1);

// Session configuration
const getSessionConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isReplit = process.env.REPL_ID !== undefined;

  const baseConfig = {
    secret: process.env.SESSION_SECRET || 'fallback-secret-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: isProduction || isReplit, // Enable secure cookies for production OR Replit
      httpOnly: true, // Prevent XSS attacks
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      sameSite: isProduction || isReplit ? 'none' as const : 'lax' as const // Use 'lax' for same-site requests (works for both local and Replit)
    }
  };

  console.log('🔧 Session config:', {
    nodeEnv: process.env.NODE_ENV,
    isReplit: isReplit,
    isProduction: isProduction,
    cookieSecure: baseConfig.cookie.secure,
    cookieSameSite: baseConfig.cookie.sameSite,
    hasDatabase: !!process.env.DATABASE_URL
  });

  if (process.env.NODE_ENV === 'local') {
    // Use memory store for local development
    console.log('🧠 Using memory session store for local development');
    return baseConfig;
  } else {
    // Use PostgreSQL store for development/production
    console.log('🗄️  Using PostgreSQL session store');
    try {
      const PgSession = ConnectPgSimple(session);
      const store = new PgSession({
        conString: process.env.DATABASE_URL,
        tableName: 'user_sessions',
        createTableIfMissing: true
      });

      // Test the store connection
      store.on('error', (err) => {
        console.error('❌ PostgreSQL session store error:', err);
      });

      return {
        ...baseConfig,
        store
      };
    } catch (error) {
      console.error('❌ Failed to create PostgreSQL session store, falling back to memory store:', error);
      return baseConfig;
    }
  }
};

app.use(session(getSessionConfig()));

// Special handling for Stripe webhooks - need raw body
app.use('/api/report/webhook', express.raw({ type: 'application/json' }));

// Regular JSON parsing for other routes
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Auth routes
app.use('/auth', authRoutes);

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      // if (logLine.length > 80) {
      //   logLine = logLine.slice(0, 79) + "…";
      // }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development" || process.env.NODE_ENV === "local") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    log(`serving on port ${port}`);
  });
})();
