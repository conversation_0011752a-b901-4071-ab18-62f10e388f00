{pkgs}: {
  deps = [
    # Mainly for chrome deps
    # Core Libraries
    pkgs.glib
    pkgs.dbus
    pkgs.nss
    pkgs.nspr
    pkgs.gtk3
    pkgs.pango
    pkgs.cairo
    pkgs.fontconfig
    pkgs.mesa

    # Accessibility and Audio
    pkgs.atk
    pkgs.at-spi2-atk
    pkgs.alsa-lib

    # X11 Graphics and Windowing System
    pkgs.xorg.libX11
    pkgs.xorg.libxcb
    pkgs.xorg.libXcomposite
    pkgs.xorg.libXdamage
    pkgs.xorg.libXfixes
    pkgs.xorg.libXrandr
    pkgs.xorg.libXrender
    pkgs.libxkbcommon
    pkgs.xorg.libXScrnSaver
    pkgs.xorg.libXext

    # Printing Support
    pkgs.cups
  ];
}
