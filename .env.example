NODE_ENV=local

# OpenAI API Key
OPENAI_API_KEY=sk-your-openai-api-key-here

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key-here
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret-here

# Frontend URL (for Stripe redirects)
FRONTEND_URL=http://localhost:5000

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production

# Database (Required for production)
DATABASE_URL=postgresql://user:password@localhost:5432/mvp_score_db

# Alternative: Use Neon, Supabase, or other PostgreSQL providers
# DATABASE_URL=postgresql://user:<EMAIL>:5432/database_name
