# Dev Container Troubleshooting Guide

## Common Issues and Solutions

### 1. "unable to find user vscode" Error

**Problem**: The container build fails with user permission errors.

**Solution**: We've updated the Dockerfile to use dev container features for Rust installation instead of manual installation.

**Files Updated**:

- `.devcontainer/Dockerfile.simple` - Simplified Dockerfile
- `.devcontainer/devcontainer.json` - Uses features for Rust installation

### 2. Container Build Fails

**Quick Fix**:

```bash
# Clear Docker cache and rebuild
docker system prune -f
docker builder prune -f

# In VS Code: Ctrl+Shift+P -> "Dev Containers: Rebuild Container"
```

### 3. Code2Prompt Not Found

**Manual Installation**:

```bash
# After container starts, install manually
source ~/.cargo/env
cargo install code2prompt

# Verify installation
code2prompt --version
```

### 4. Rust/Cargo Not Available

**Check Installation**:

```bash
# Check if Rust is installed
rustc --version
cargo --version

# If not, install manually
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source ~/.cargo/env
```

### 5. Port Conflicts

**Check Ports**:

```bash
# Check what's using the ports
sudo lsof -i :3000
sudo lsof -i :5000

# Kill processes if needed
sudo kill -9 <PID>
```

### 6. Stripe CLI Issues

**Check Installation**:

```bash
# Verify Stripe CLI is installed
stripe --version

# Authenticate with Stripe
stripe login

# Test webhook forwarding
stripe listen --forward-to localhost:5000/api/stripe/webhook
```

**Common Stripe Issues**:

```bash
# If authentication fails
stripe logout
stripe login

# If webhook forwarding fails
# Check if port 5000 is available
lsof -i :5000

# Test webhook endpoint manually
curl -X POST http://localhost:5000/api/stripe/webhook \
     -H 'Content-Type: application/json' \
     -d '{"type": "test.event"}'
```

### 7. Environment Variables Not Loading

**Fix**:

```bash
# Create .env file if missing
cp .env.example .env

# Edit with your values
nano .env

# Restart container
# In VS Code: Ctrl+Shift+P -> "Dev Containers: Rebuild Container"
```

## Alternative Setup Methods

### Method 1: Simple Container (Recommended)

Use the simplified setup that relies on dev container features:

```json
// .devcontainer/devcontainer.json
{
  "name": "MVP ScoreWidget Dev Environment",
  "build": {
    "dockerfile": "Dockerfile.simple",
    "context": ".."
  },
  "features": {
    "ghcr.io/devcontainers/features/rust:1": {
      "version": "latest",
      "profile": "default"
    }
  }
}
```

### Method 2: Manual Installation

If the automated setup fails, install Code2Prompt manually after container starts:

```bash
# 1. Start container without Code2Prompt
# 2. Install Rust manually
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source ~/.cargo/env

# 3. Install Code2Prompt
cargo install code2prompt

# 4. Test installation
code2prompt --version
```

### Method 3: Local Installation (Not Recommended)

If you prefer to install Code2Prompt locally instead of in container:

```bash
# Install Rust locally
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y

# Install Code2Prompt
cargo install code2prompt

# Use without dev container
npm install
npm run dev
```

## Verification Steps

After successful container setup:

### 1. Check Environment

```bash
# Verify Node.js
node --version
npm --version

# Verify Rust
rustc --version
cargo --version

# Verify Code2Prompt
code2prompt --version

# Verify Stripe CLI
stripe --version
```

### 2. Test Code2Prompt

```bash
# Run test script
./scripts/test-enhanced-report.sh

# Or test manually
mkdir test-project
echo "console.log('test')" > test-project/index.js
code2prompt test-project --output test.txt
cat test.txt
```

### 3. Test Development Server

```bash
# Start development
npm run dev

# Check if accessible
curl http://localhost:5000/api/health
```

## Getting Help

### 1. Check Container Logs

```bash
# View container logs
docker logs <container-name>

# View build logs
# In VS Code: View -> Output -> Dev Containers
```

### 2. Debug Container

```bash
# Access container shell
docker exec -it <container-name> bash

# Check file permissions
ls -la /home/<USER>/
ls -la /home/<USER>/.cargo/
```

### 3. Reset Everything

```bash
# Complete reset
docker system prune -a -f
docker volume prune -f

# Remove VS Code dev container cache
rm -rf ~/.vscode-remote-containers/

# Rebuild from scratch
# In VS Code: Ctrl+Shift+P -> "Dev Containers: Rebuild Container"
```

## Success Indicators

You'll know the setup is working when:

✅ Container builds without errors
✅ VS Code opens in container successfully
✅ `code2prompt --version` works
✅ `stripe --version` works
✅ `npm run dev` starts the server
✅ All ports are accessible (3000, 5000, 4242)
✅ Test script `./scripts/test-enhanced-report.sh` passes
✅ Stripe webhook forwarding works: `./scripts/stripe-webhooks.sh`

## Contact

If you continue to have issues:

1. Check the container logs for specific error messages
2. Try the manual installation method
3. Ensure Docker has enough resources allocated
4. Check that your system meets the prerequisites

The dev container setup provides an isolated environment for enhanced report generation with Code2Prompt, but you can always fall back to local development if needed.
