# MVP ScoreWidget Dev Container

This dev container provides a complete development environment for the MVP ScoreWidget project with enhanced report generation capabilities.

## 🚀 Features

### Pre-installed Tools

- **Node.js 20** with npm and essential packages
- **TypeScript** and tsx for development
- **Code2Prompt** for comprehensive codebase analysis
- **Rust** (required for Code2Prompt)
- **Chrome** for Puppeteer PDF generation
- **PostgreSQL client** for database operations
- **Stripe CLI** for payment testing and webhook forwarding

### VS Code Extensions

- **Augment Code** - AI-powered coding assistant
- **TypeScript** support with latest features
- **Tailwind CSS** IntelliSense
- **Prettier** for code formatting
- **ESLint** for code quality
- **GitLens** for enhanced Git integration
- **React** development tools

### Enhanced Report Generation

- **80-90% Codebase Coverage** using Code2Prompt
- **Advanced OpenAI Models** (o1-mini, o1-preview)
- **Custom Analysis Templates** for different report types
- **Automated Testing Scripts** for report generation

## 🛠 Setup Instructions

### 1. Prerequisites

- **Docker** installed on your system
- **VS Code** with Dev Containers extension
- **Git** for version control

### 2. Open in Dev Container

#### Option A: VS Code Command Palette

1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
3. Type "Dev Containers: Reopen in Container"
4. Select the command and wait for container to build

#### Option B: VS Code UI

1. Open the project folder in VS Code
2. Click the blue button in the bottom-left corner
3. Select "Reopen in Container"

#### Option C: Command Line

```bash
# Clone the repository
git clone <your-repo-url>
cd MVPScoreWidget

# Open in VS Code
code .

# VS Code will prompt to reopen in container
```

### 3. First-Time Setup

After the container builds and starts:

1. **Configure Environment Variables**:

   ```bash
   # Edit the .env file with your actual values
   cp .env .env.local
   nano .env
   ```

2. **Install Dependencies**:

   ```bash
   npm install
   ```

3. **Test Code2Prompt**:

   ```bash
   ./scripts/test-enhanced-report.sh
   ```

4. **Start Development**:
   ```bash
   npm run dev
   ```

## 💳 Stripe CLI Usage

### Authentication and Setup

```bash
# Authenticate with Stripe
stripe login

# Verify authentication
stripe config --list
```

### Webhook Testing

```bash
# Start webhook forwarding to your local server
./scripts/stripe-webhooks.sh

# Or manually:
stripe listen --forward-to localhost:5000/api/stripe/webhook
```

### Testing Payment Events

```bash
# Trigger test events
stripe trigger payment_intent.succeeded
stripe trigger payment_intent.payment_failed
stripe trigger checkout.session.completed

# View recent events
stripe events list --limit 10
```

### Interactive Testing Menu

```bash
# Run the interactive Stripe testing script
./scripts/stripe-test.sh
```

## 📊 Code2Prompt Usage

### Basic Analysis

```bash
# Analyze entire project
code2prompt . --output analysis.txt

# Exclude common build artifacts
code2prompt . \
  --exclude "node_modules/**,dist/**,build/**,*.log" \
  --output analysis.txt
```

### Enhanced Analysis with Templates

```bash
# Comprehensive analysis
code2prompt . \
  --template .code2prompt/templates/comprehensive.hbs \
  --output comprehensive-analysis.txt

# Security-focused analysis
code2prompt . \
  --template .code2prompt/templates/security.hbs \
  --output security-analysis.txt
```

### Integration with Report Generation

```typescript
// Use in your enhanced report generation
const enhancedReport = await generateEnhancedFullReport(
  analysis,
  '/workspace',
  {
    useAdvancedModels: true,
    useCode2Prompt: true,
    analysisDepth: 'COMPREHENSIVE',
  }
);
```

## 🔧 Available Services

The dev container includes these services:

| Service             | Port | Description                       |
| ------------------- | ---- | --------------------------------- |
| **Frontend**        | 3000 | Vite development server           |
| **Backend**         | 5000 | Express API server                |
| **Stripe Webhooks** | 4242 | Stripe CLI webhook forwarding     |
| **PostgreSQL**      | 5432 | Local database (optional)         |
| **Redis**           | 6379 | Caching and sessions (optional)   |
| **Adminer**         | 8081 | Database management UI (optional) |

### Access Services

```bash
# Frontend
open http://localhost:3000

# Backend API
curl http://localhost:5000/api/health

# Database UI (Adminer) - if using docker-compose
open http://localhost:8081
# Server: postgres, User: postgres, Password: postgres, Database: mvp_scorewidget

# Test Stripe webhook endpoint
curl -X POST http://localhost:5000/api/stripe/webhook \
     -H 'Content-Type: application/json' \
     -d '{"type": "test.event"}'
```

## 🔧 Development Workflow

### 1. Standard Development

```bash
# Start development server
npm run dev

# Type checking
npm run check

# Build for production
npm run build
```

### 2. Enhanced Report Testing

```bash
# Test Code2Prompt analysis
./scripts/test-enhanced-report.sh

# Start development with all checks
./scripts/dev-start.sh
```

### 3. Database Operations

```bash
# Push schema changes
npm run db:push

# Connect to database (if using PostgreSQL)
psql $DATABASE_URL
```

## 📁 Project Structure

```
.devcontainer/
├── devcontainer.json     # Dev container configuration
├── Dockerfile           # Container image definition
├── post-create.sh       # Setup script
└── README.md           # This file

.code2prompt/
└── templates/
    ├── comprehensive.hbs # Full analysis template
    └── security.hbs     # Security-focused template

scripts/
├── dev-start.sh         # Development startup script
└── test-enhanced-report.sh # Code2Prompt testing script
```

## 🎯 Enhanced Report Generation Features

### 1. Comprehensive Codebase Analysis

- **Full Project Coverage**: Analyzes 80-90% of your codebase
- **Smart Filtering**: Excludes build artifacts and dependencies
- **Technology Detection**: Automatically identifies frameworks and libraries
- **Code Metrics**: Provides detailed code quality metrics

### 2. Advanced AI Models

- **o1-mini**: Cost-effective reasoning for strategic insights
- **o1-preview**: Advanced reasoning for complex analysis
- **GPT-4o**: Fast general analysis and content generation

### 3. Custom Analysis Types

- **Architectural Analysis**: Design patterns and code structure
- **Security Assessment**: Vulnerability identification and recommendations
- **Business Readiness**: Market fit and user experience evaluation
- **Performance Analysis**: Optimization opportunities and bottlenecks

## 🔍 Troubleshooting

### Code2Prompt Issues

```bash
# Verify installation
code2prompt --version

# Reinstall if needed
cargo install code2prompt --force

# Test with simple project
mkdir test && echo "console.log('test')" > test/index.js
code2prompt test --output test.txt
```

### Container Issues

```bash
# Rebuild container
# In VS Code: Ctrl+Shift+P -> "Dev Containers: Rebuild Container"

# Check container logs
docker logs <container-id>

# Reset container
# In VS Code: Ctrl+Shift+P -> "Dev Containers: Rebuild and Reopen in Container"
```

### Environment Issues

```bash
# Check environment variables
printenv | grep -E "(NODE_ENV|OPENAI_API_KEY|DATABASE_URL)"

# Reload environment
source ~/.bashrc

# Check Node.js and npm versions
node --version && npm --version
```

## 📚 Additional Resources

- [Code2Prompt Documentation](https://github.com/mufeedvh/code2prompt)
- [OpenAI API Documentation](https://platform.openai.com/docs)
- [VS Code Dev Containers](https://code.visualstudio.com/docs/devcontainers/containers)
- [Augment Code Extension](https://marketplace.visualstudio.com/items?itemName=augmentcode.augment)

## 🤝 Contributing

1. Make changes in the dev container
2. Test enhanced report generation
3. Run type checking: `npm run check`
4. Commit changes with descriptive messages
5. Push to your branch and create a pull request

## 🎉 Ready to Go!

Your development environment is now set up with:

- ✅ Code2Prompt for comprehensive codebase analysis
- ✅ Advanced OpenAI models for enhanced insights
- ✅ Augment Code extension for AI-powered development
- ✅ All necessary tools and dependencies
- ✅ Custom templates and scripts for enhanced report generation

Start developing with `npm run dev` and enjoy 80-90% codebase coverage in your MVP reports! 🚀
