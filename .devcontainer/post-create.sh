#!/bin/bash

# Post-creation script for MVP ScoreWidget dev container
echo "🚀 Setting up MVP ScoreWidget development environment..."

# Ensure we're in the workspace directory
if [ -d "/workspaces" ]; then
    cd /workspaces/*
elif [ -d "/workspace" ]; then
    cd /workspace
else
    echo "⚠️  Workspace directory not found, staying in current directory"
fi

echo "📁 Working directory: $(pwd)"

# Install/update Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

# Set up environment variables template if it doesn't exist
if [ ! -f .env ]; then
    echo "🔧 Creating .env template..."
    cat > .env << EOL
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration
DATABASE_URL=your_database_url_here

# Stripe Configuration (for payments)
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Google OAuth (if using)
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Replit Object Storage (if using)
REPLIT_DB_URL=your_replit_db_url_here

# Development Configuration
NODE_ENV=development
PORT=5000
EOL
    echo "✅ Created .env template. Please update with your actual values."
fi

# Set up Rust and Cargo environment (dev container features should have installed Rust)
echo "🦀 Setting up Rust environment..."

# Source Rust environment if available
if [ -f "$HOME/.cargo/env" ]; then
    source "$HOME/.cargo/env"
fi

# Add to PATH
export PATH="$HOME/.cargo/bin:$PATH"

# Verify Rust installation
if command -v rustc &> /dev/null; then
    echo "✅ Rust is installed: $(rustc --version)"
    echo "✅ Cargo is installed: $(cargo --version)"
else
    echo "⚠️  Rust not found, dev container features may still be setting up"
fi

# Install Code2Prompt
echo "🔧 Installing Code2Prompt..."
if command -v cargo &> /dev/null; then
    echo "📦 Installing Code2Prompt via Cargo..."
    cargo install code2prompt

    # Verify installation
    if command -v code2prompt &> /dev/null || [ -f "$HOME/.cargo/bin/code2prompt" ]; then
        echo "✅ Code2Prompt installed successfully!"

        # Test Code2Prompt with a simple example
        echo "🧪 Testing Code2Prompt..."
        mkdir -p /tmp/test-project
        echo "console.log('Hello, Code2Prompt!');" > /tmp/test-project/test.js
        echo "# Test Project" > /tmp/test-project/README.md

        if code2prompt /tmp/test-project --output /tmp/test-output.txt 2>/dev/null; then
            echo "✅ Code2Prompt test successful!"
            rm -rf /tmp/test-project /tmp/test-output.txt
        else
            echo "⚠️  Code2Prompt test failed, but installation appears complete"
            rm -rf /tmp/test-project /tmp/test-output.txt
        fi
    else
        echo "❌ Code2Prompt installation failed"
    fi
else
    echo "❌ Cargo not available, Code2Prompt installation skipped"
    echo "   You can install it manually later with: cargo install code2prompt"
fi

# Set up git hooks (if .git exists)
if [ -d .git ]; then
    echo "🔧 Setting up git hooks..."
    # Pre-commit hook for code formatting
    cat > .git/hooks/pre-commit << 'EOL'
#!/bin/bash
# Run TypeScript check
npm run check
if [ $? -ne 0 ]; then
    echo "❌ TypeScript check failed. Please fix errors before committing."
    exit 1
fi
echo "✅ TypeScript check passed"
EOL
    chmod +x .git/hooks/pre-commit
fi

# Create useful development scripts
echo "📝 Creating development scripts..."
mkdir -p scripts

# Script to generate enhanced reports
cat > scripts/test-enhanced-report.sh << 'EOL'
#!/bin/bash
# Test script for enhanced report generation

echo "🧪 Testing Enhanced Report Generation..."

# Check if Code2Prompt is available
if ! command -v code2prompt &> /dev/null; then
    echo "❌ Code2Prompt not found. Please install it first."
    exit 1
fi

# Test with current project
echo "📊 Analyzing current project with Code2Prompt..."
code2prompt . \
    --output /tmp/project-analysis.txt \
    --exclude "node_modules/**,dist/**,build/**,*.log,.git/**" \
    --line-numbers \
    --tokens

if [ -f /tmp/project-analysis.txt ]; then
    echo "✅ Code2Prompt analysis complete!"
    echo "📄 Output saved to /tmp/project-analysis.txt"
    echo "📊 File size: $(du -h /tmp/project-analysis.txt | cut -f1)"
    echo "📝 Line count: $(wc -l < /tmp/project-analysis.txt)"
else
    echo "❌ Code2Prompt analysis failed"
fi
EOL

chmod +x scripts/test-enhanced-report.sh

# Script to start development environment
cat > scripts/dev-start.sh << 'EOL'
#!/bin/bash
# Start development environment

echo "🚀 Starting MVP ScoreWidget development environment..."

# Check environment variables
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Please create one based on .env template."
    exit 1
fi

# Start the development server
echo "🔧 Starting development server..."
npm run dev
EOL

chmod +x scripts/dev-start.sh

# Create Code2Prompt configuration templates
echo "⚙️ Creating Code2Prompt templates..."
mkdir -p .code2prompt/templates

# Template for comprehensive analysis
cat > .code2prompt/templates/comprehensive.hbs << 'EOL'
# Comprehensive MVP Analysis

## Project Overview
**Project:** {{project_name}}
**Analysis Date:** {{date}}
**Coverage:** {{coverage_percentage}}%

## Project Structure
```
{{tree}}
```

## Technologies Detected
{{#each technologies}}
- {{this}}
{{/each}}

## File Analysis
{{#each files}}
### {{path}}
**Type:** {{extension}}
**Lines:** {{line_count}}

```{{extension}}
{{content}}
```

---
{{/each}}

## Analysis Instructions
Please provide a comprehensive MVP readiness analysis covering:

1. **Architecture & Design Patterns**
   - Code organization and structure
   - Design patterns used
   - Architectural decisions

2. **Code Quality & Best Practices**
   - Code quality assessment
   - Adherence to best practices
   - Technical debt identification

3. **Security Assessment**
   - Security vulnerabilities
   - Authentication and authorization
   - Data protection measures

4. **Performance Considerations**
   - Performance bottlenecks
   - Optimization opportunities
   - Scalability concerns

5. **Testing Strategy**
   - Test coverage analysis
   - Testing framework usage
   - Quality assurance practices

6. **Documentation Quality**
   - Code documentation
   - API documentation
   - User documentation

7. **Deployment Readiness**
   - Production readiness
   - CI/CD setup
   - Monitoring and logging

8. **Business Value & User Experience**
   - Feature completeness
   - User experience quality
   - Market readiness

Focus on actionable insights for rapid MVP iteration and improvement.
EOL

# Template for security analysis
cat > .code2prompt/templates/security.hbs << 'EOL'
# Security Analysis

## Project: {{project_name}}

## Security-Critical Files
{{#each files}}
{{#if (contains path "auth")}}
### {{path}}
```{{extension}}
{{content}}
```
{{/if}}
{{#if (contains path "security")}}
### {{path}}
```{{extension}}
{{content}}
```
{{/if}}
{{#if (contains path "config")}}
### {{path}}
```{{extension}}
{{content}}
```
{{/if}}
{{/each}}

## Security Analysis Request
Conduct a comprehensive security analysis focusing on:
1. Authentication and authorization mechanisms
2. Data protection and encryption
3. Input validation and sanitization
4. API security measures
5. Dependency vulnerabilities
6. Configuration security
7. Error handling and information disclosure
8. Session management
9. CSRF and XSS protection
10. SQL injection prevention

Provide specific recommendations for each identified issue.
EOL

echo "✅ Development environment setup complete!"
echo ""
echo "🎉 Welcome to MVP ScoreWidget Development Environment!"
echo ""
echo "📋 Available commands:"
echo "  npm run dev          - Start development server"
echo "  npm run build        - Build for production"
echo "  npm run check        - TypeScript type checking"
echo "  npm run db:push      - Push database schema"
echo "  code2prompt --help   - Code2Prompt help"
echo ""
echo "🔧 Useful scripts:"
echo "  ./scripts/dev-start.sh              - Start development with checks"
echo "  ./scripts/test-enhanced-report.sh   - Test Code2Prompt analysis"
echo ""
echo "📁 Code2Prompt templates available in .code2prompt/templates/"
echo ""
echo "🚀 Ready to enhance your MVP reports with 80-90% codebase coverage!"
