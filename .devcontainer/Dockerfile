# Use the official Node.js image as base
FROM mcr.microsoft.com/vscode/devcontainers/typescript-node:20-bullseye

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=development

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Essential build tools
    build-essential \
    curl \
    wget \
    git \
    unzip \
    # Chrome dependencies for Puppeteer
    libnss3-dev \
    libatk-bridge2.0-dev \
    libdrm2 \
    libxkbcommon0 \
    libgtk-3-dev \
    libxss1 \
    libasound2 \
    # Additional utilities
    jq \
    tree \
    htop \
    vim \
    nano \
    # PostgreSQL client
    postgresql-client \
    # Python for some build tools
    python3 \
    python3-pip \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install global npm packages
RUN npm install -g \
    typescript \
    tsx \
    nodemon \
    pm2 \
    @types/node

# Create workspace directory
WORKDIR /workspaces

# Switch to vscode user for remaining operations
USER vscode

# Set up shell environment
RUN echo 'alias ll="ls -la"' >> /home/<USER>/.bashrc
RUN echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> /home/<USER>/.bashrc

# Verify Node.js installation
RUN node --version
RUN npm --version

# Set the default command
CMD ["bash"]
