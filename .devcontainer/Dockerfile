# Use the official Node.js image as base
FROM mcr.microsoft.com/vscode/devcontainers/typescript-node:20-bullseye

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=development

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Essential build tools
    build-essential \
    curl \
    wget \
    git \
    unzip \
    # Chrome dependencies for Puppeteer
    libnss3-dev \
    libatk-bridge2.0-dev \
    libdrm2 \
    libxkbcommon0 \
    libgtk-3-dev \
    libxss1 \
    libasound2 \
    # Additional utilities
    jq \
    tree \
    htop \
    vim \
    nano \
    # PostgreSQL client
    postgresql-client \
    # Python for some build tools
    python3 \
    python3-pip \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Rust (required for Code2Prompt)
USER vscode
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
ENV PATH="/home/<USER>/.cargo/bin:${PATH}"

# Install Code2Prompt
RUN /home/<USER>/.cargo/bin/cargo install code2prompt

# Switch back to root for system-level installations
USER root

# Install global npm packages
RUN npm install -g \
    typescript \
    tsx \
    nodemon \
    pm2 \
    @types/node

# Create workspace directory
WORKDIR /workspace

# Copy package files for dependency installation
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci

# Install Chrome for Puppeteer (using the same approach as in package.json)
RUN npx @puppeteer/browsers install chrome

# Set up proper permissions
RUN chown -R vscode:vscode /workspace
RUN chown -R vscode:vscode /home/<USER>/.cargo

# Switch back to vscode user
USER vscode

# Set up shell environment
RUN echo 'export PATH="/home/<USER>/.cargo/bin:$PATH"' >> /home/<USER>/.bashrc
RUN echo 'alias ll="ls -la"' >> /home/<USER>/.bashrc
RUN echo 'alias code2prompt="code2prompt"' >> /home/<USER>/.bashrc

# Verify installations
RUN node --version
RUN npm --version
RUN /home/<USER>/.cargo/bin/cargo --version
RUN /home/<USER>/.cargo/bin/code2prompt --version

# Set the default command
CMD ["bash"]
