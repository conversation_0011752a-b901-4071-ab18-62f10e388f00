-- Initialize MVP ScoreWidget database
-- This script sets up the basic database structure for local development

-- Create database if it doesn't exist
-- (PostgreSQL in Docker will create the database specified in POSTGRES_DB)

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create a basic user for development (optional)
-- The main schema will be managed by Drizzle ORM

-- Set timezone
SET timezone = 'UTC';

-- Create basic indexes for performance (these will be managed by Dr<PERSON>zle)
-- This is just a placeholder for any initial setup needed

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'MVP ScoreWidget database initialized successfully!';
END $$;
