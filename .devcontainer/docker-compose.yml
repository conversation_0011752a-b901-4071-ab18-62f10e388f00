version: '3.8'

services:
  # Main development container
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      - ../..:/workspaces:cached
      - /var/run/docker.sock:/var/run/docker-host.sock
    command: sleep infinity
    environment:
      - NODE_ENV=development
    ports:
      - "3000:3000"  # Frontend (Vite)
      - "5000:5000"  # Backend (Express)
      - "8080:8080"  # Additional services
    networks:
      - mvp-network

  # PostgreSQL database (optional - for local development)
  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: mvp_scorewidget
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - mvp-network

  # Redis (optional - for caching and sessions)
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - mvp-network

  # Adminer (optional - database management UI)
  adminer:
    image: adminer:latest
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    networks:
      - mvp-network
    depends_on:
      - postgres

volumes:
  postgres-data:
  redis-data:

networks:
  mvp-network:
    driver: bridge
