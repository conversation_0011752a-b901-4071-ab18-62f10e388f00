{
  "name": "MVP ScoreWidget Dev Environment",
  "build": {
    "dockerfile": "Dockerfile.simple",
    "context": ".."
  },
  // Configure tool-specific properties
  "customizations": {
    "vscode": {
      "extensions": [
        // Essential extensions
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "ms-vscode.vscode-json",
        // React and frontend
        "ms-vscode.vscode-react-refactor",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",
        // Database and API
        "ms-vscode.vscode-postgres",
        "humao.rest-client",
        // Git and collaboration
        "eamodio.gitlens",
        "github.vscode-pull-request-github",
        // Augment Code extension (AI-powered coding assistant)
        "augmentcode.augment",
        // Additional helpful extensions
        "ms-vscode.vscode-docker",
        "ms-vscode.vscode-eslint",
        "ms-vscode.vscode-npm-scripts",
        "christian-kohler.npm-intellisense",
        "ms-vscode.vscode-markdown-preview-enhanced"
      ],
      "settings": {
        "typescript.preferences.importModuleSpecifier": "relative",
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": "explicit"
        },
        "tailwindCSS.includeLanguages": {
          "typescript": "javascript",
          "typescriptreact": "javascript"
        },
        "files.associations": {
          "*.css": "tailwindcss"
        },
        "terminal.integrated.defaultProfile.linux": "bash",
        "terminal.integrated.profiles.linux": {
          "bash": {
            "path": "/bin/bash"
          }
        }
      }
    }
  },
  // Forward ports for development
  "forwardPorts": [
    3000,
    5000,
    8080
  ],
  "portsAttributes": {
    "3000": {
      "label": "Frontend (Vite)",
      "onAutoForward": "notify"
    },
    "5000": {
      "label": "Backend (Express)",
      "onAutoForward": "notify"
    },
    "8080": {
      "label": "Additional Services",
      "onAutoForward": "silent"
    }
  },
  // Container configuration
  "runArgs": [
    "--init",
    "--privileged"
  ],
  // Environment variables
  "containerEnv": {
    "NODE_ENV": "development",
    "TERM": "xterm-256color"
  },
  // Commands to run after container creation
  "postCreateCommand": "bash .devcontainer/post-create.sh",
  // Commands to run when attaching to the container
  "postAttachCommand": "echo 'Welcome to MVP ScoreWidget Dev Environment! 🚀'",
  // Features to install
  "features": {
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {},
    "ghcr.io/devcontainers/features/docker-in-docker:2": {},
    "ghcr.io/devcontainers/features/rust:1": {
      "version": "latest",
      "profile": "default"
    }
  },
  // User configuration
  "remoteUser": "vscode",
  "updateRemoteUserUID": true,
  // Lifecycle scripts
  "initializeCommand": "echo 'Initializing MVP ScoreWidget development environment...'",
  "onCreateCommand": "echo 'Container created successfully!'",
  // Shutdown action
  "shutdownAction": "stopContainer"
}