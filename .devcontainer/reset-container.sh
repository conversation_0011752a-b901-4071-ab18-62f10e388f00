#!/bin/bash
# Script to reset dev container and clear Docker cache

echo "🧹 Resetting Dev Container Environment"

# Stop any running containers
echo "🛑 Stopping any running containers..."
docker stop $(docker ps -q --filter "label=devcontainer.local_folder") 2>/dev/null || true

# Remove dev container images
echo "🗑️  Removing dev container images..."
docker rmi $(docker images -q --filter "reference=vsc-mvpscorewidget*") 2>/dev/null || true

# Clean up Docker system
echo "🧽 Cleaning Docker system..."
docker system prune -f
docker builder prune -f

# Remove Docker volumes (optional - uncomment if needed)
# echo "📦 Removing Docker volumes..."
# docker volume prune -f

echo "✅ Docker cleanup complete!"
echo ""
echo "📋 Next steps:"
echo "1. In VS Code: Ctrl+Shift+P"
echo "2. Run: 'Dev Containers: Rebuild Container'"
echo "3. Wait for the container to build and start"
echo ""
echo "🚀 Your dev container should now build successfully!"
