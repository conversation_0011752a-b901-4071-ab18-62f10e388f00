# Fixed Dockerfile for MVP ScoreWidget Dev Container
FROM node:20-bullseye

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=development

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Essential build tools
    build-essential \
    curl \
    wget \
    git \
    unzip \
    sudo \
    # Chrome dependencies for Puppeteer
    libnss3-dev \
    libatk-bridge2.0-dev \
    libdrm2 \
    libxkbcommon0 \
    libgtk-3-dev \
    libxss1 \
    libasound2 \
    # Additional utilities
    jq \
    tree \
    htop \
    vim \
    nano \
    # PostgreSQL client
    postgresql-client \
    # Python for some build tools
    python3 \
    python3-pip \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Stripe CLI
RUN curl -s https://packages.stripe.dev/api/security/keypair/stripe-cli-gpg/public | gpg --dearmor | tee /usr/share/keyrings/stripe.gpg \
    && echo "deb [signed-by=/usr/share/keyrings/stripe.gpg] https://packages.stripe.dev/stripe-cli-debian-local stable main" | tee -a /etc/apt/sources.list.d/stripe.list \
    && apt-get update \
    && apt-get install -y stripe \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create vscode user with sudo privileges
RUN groupadd --gid 1000 vscode \
    && useradd --uid 1000 --gid vscode --shell /bin/bash --create-home vscode \
    && echo vscode ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/vscode \
    && chmod 0440 /etc/sudoers.d/vscode

# Install global npm packages
RUN npm install -g \
    typescript \
    tsx \
    nodemon \
    pm2 \
    @types/node

# Create workspace directory and set permissions
RUN mkdir -p /workspaces && chown -R vscode:vscode /workspaces

# Switch to vscode user
USER vscode

# Set working directory
WORKDIR /workspaces

# Set up shell environment for vscode user
RUN echo 'alias ll="ls -la"' >> ~/.bashrc \
    && echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.bashrc \
    && echo 'export CARGO_HOME="$HOME/.cargo"' >> ~/.bashrc \
    && echo 'export RUSTUP_HOME="$HOME/.rustup"' >> ~/.bashrc

# Verify installations
RUN node --version && npm --version

# Set the default command
CMD ["bash"]
