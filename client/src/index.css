@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-neutral-50 text-neutral-800;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-neutral-900 font-semibold;
  }
}

@layer components {
  .status-circle {
    @apply flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center;
  }
  
  .status-circle-completed {
    @apply bg-blue-100 text-blue-600;
  }
  
  .status-circle-current {
    @apply bg-blue-500 text-white animate-pulse;
  }
  
  .status-circle-pending {
    @apply bg-neutral-200 text-neutral-500;
  }
}
