import { GoogleLogin } from '@react-oauth/google';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface AuthRequiredModalProps {
  isOpen: boolean;
  onAuthSuccess: () => void;
}

export default function AuthRequiredModal({ isOpen, onAuthSuccess }: AuthRequiredModalProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const loginMutation = useMutation({
    mutationFn: async (credential: string) => {
      const response = await fetch('/auth/google', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ credential }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Login failed');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/auth/me'] });
      toast({
        title: "Welcome!",
        description: "You've been successfully signed in.",
      });
      onAuthSuccess();
    },
    onError: (error: Error) => {
      toast({
        title: "Sign in failed",
        description: error.message || "Please try again.",
        variant: "destructive",
      });
    }
  });

  return (
    <Dialog open={isOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Sign in to view your report</DialogTitle>
          <DialogDescription>
            Your analysis is complete! Please sign in with Google to view your MVP readiness report.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex justify-center py-4">
          <GoogleLogin
            onSuccess={(credentialResponse) => {
              if (credentialResponse.credential) {
                loginMutation.mutate(credentialResponse.credential);
              }
            }}
            onError={() => {
              toast({
                title: "Sign in failed",
                description: "Please try again.",
                variant: "destructive",
              });
            }}
            theme="outline"
            size="large"
            text="continue_with"
          />
        </div>
        
        <p className="text-xs text-gray-500 text-center">
          By signing in, you agree to our Terms of Service and Privacy Policy
        </p>
      </DialogContent>
    </Dialog>
  );
}
