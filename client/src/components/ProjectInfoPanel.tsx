import { Card } from "@/components/ui/card";
import { ProjectInfo } from "@/pages/Home";

interface ProjectInfoPanelProps {
  projectInfo: ProjectInfo;
}

export default function ProjectInfoPanel({ projectInfo }: ProjectInfoPanelProps) {
  return (
    <Card className="mt-6 bg-white rounded-xl shadow-md overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-semibold text-neutral-900 mb-4">Project Summary</h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-neutral-500">Project Name</h3>
            <p className="font-medium">{projectInfo.name}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-neutral-500">Project Type</h3>
            <p>{projectInfo.type}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-neutral-500">Files Analyzed</h3>
            <p>{projectInfo.fileCount} files</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-neutral-500">Technologies Detected</h3>
            <div className="flex flex-wrap gap-2 mt-1">
              {projectInfo.technologies.map((tech, index) => (
                <span key={index} className="px-2 py-1 bg-neutral-100 text-neutral-800 rounded text-xs">
                  {tech}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
