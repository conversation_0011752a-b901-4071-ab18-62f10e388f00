import { Card } from "@/components/ui/card";
import { ProgressInfo } from "@/pages/Home";

interface AnalysisProgressProps {
  progress: ProgressInfo;
}

export default function AnalysisProgress({ progress }: AnalysisProgressProps) {
  return (
    <Card className="bg-white rounded-xl shadow-md overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-semibold text-neutral-900 mb-4">VibeComplete Analysis in Progress</h2>
        
        <div className="space-y-6">
          {/* Progress Bar */}
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="font-medium text-neutral-700">{progress.currentStep}</span>
              <span className="text-neutral-500">{progress.percentage}%</span>
            </div>
            <div className="w-full bg-neutral-200 rounded-full h-2.5">
              <div 
                className="bg-primary-500 h-2.5 rounded-full" 
                style={{ width: `${progress.percentage}%` }}
              />
            </div>
          </div>
          
          {/* Step Indicators */}
          <div className="space-y-4">
            {progress.steps.map((step, index) => (
              <div className="flex items-center" key={index}>
                <div className={`status-circle ${
                  step.status === 'completed' ? 'status-circle-completed' : 
                  step.status === 'current' ? 'status-circle-current' : 
                  'status-circle-pending'
                }`}>
                  {step.status === 'completed' ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : step.status === 'current' ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-neutral-900">{step.name}</p>
                  <p className="text-xs text-neutral-500">
                    {step.status === 'completed' && step.duration 
                      ? `Completed in ${step.duration}`
                      : step.status === 'current' 
                        ? 'In progress...' 
                        : 'Waiting to start'}
                  </p>
                </div>
              </div>
            ))}
          </div>
          
          <p className="text-sm text-neutral-500 text-center">
            This analysis typically takes 30-60 seconds for small to medium projects.
          </p>
        </div>
      </div>
    </Card>
  );
}
