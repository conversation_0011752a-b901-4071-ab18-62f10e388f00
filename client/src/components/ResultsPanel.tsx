import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import RadarChart from "./RadarChart";
import { ScoreData } from "@/pages/Home";
import { useLocation } from "wouter";

interface ResultsPanelProps {
  scoreData: ScoreData;
  analysisId?: number | null;
}

export default function ResultsPanel({ scoreData, analysisId }: ResultsPanelProps) {
  const [, navigate] = useLocation();
  
  const viewPremiumReport = () => {
    if (!analysisId) {
      console.error("No analysis ID available");
      return;
    }
    // Navigate to the report preview page
    navigate(`/report/preview/${analysisId}`);
  };
  
  return (
    <div>
      {/* Score Summary Card */}
      <Card className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-neutral-900">MVP Readiness Score</h2>
            <div>
              <span className="text-3xl font-bold text-primary-600">{scoreData.total}</span>
              <span className="text-neutral-500">/40</span>
            </div>
          </div>
          
          <div className="mb-6">
            <div className="h-3 w-full bg-neutral-200 rounded-full">
              <div 
                className="h-3 rounded-full" 
                style={{ 
                  width: `${scoreData.percentage}%`, 
                  backgroundColor: scoreData.color 
                }}
              ></div>
            </div>
            <div className="flex justify-between text-xs text-neutral-500 mt-1">
              <span>Pre-MVP</span>
              <span>Early MVP</span>
              <span>Solid MVP</span>
              <span>Advanced MVP</span>
            </div>
          </div>
          
          <div className="p-4 rounded-lg" style={{ backgroundColor: "#ECFDF5" }}>
            <div className="flex">
              <div className="flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-emerald-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-emerald-600">{scoreData.category}</h3>
                <p className="text-sm text-emerald-500 mt-1">
                  {scoreData.category === "Solid MVP (26-35 points)" 
                    ? "Your project is ready for user feedback and has the essentials in place."
                    : scoreData.category === "Advanced MVP (36-40 points)"
                      ? "Your project exceeds MVP requirements and is approaching a complete product."
                      : scoreData.category === "Early MVP (16-25 points)"
                        ? "Your project has basic viability but needs more development before release."
                        : "Your project requires significant work to reach MVP status."}
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>
      
      {/* Charts and Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Radar Chart */}
        <Card className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-neutral-900 mb-4">Score Breakdown</h3>
            <div className="h-72">
              <RadarChart data={scoreData.radarData} />
            </div>
          </div>
        </Card>
        
        {/* Top Strengths & Weaknesses */}
        <Card className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-neutral-900 mb-4">Key Insights</h3>
            
            <div className="space-y-5">
              <div>
                <h4 className="text-sm font-medium text-emerald-600 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
                  </svg>
                  Strengths
                </h4>
                <ul className="mt-2 space-y-2">
                  {scoreData.strengths.map((strength, index) => (
                    <li className="flex" key={index}>
                      <span className="text-emerald-500 mr-2">•</span>
                      <span className="text-sm text-neutral-700">{strength}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-red-500 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clipRule="evenodd" />
                  </svg>
                  Areas for Improvement
                </h4>
                <ul className="mt-2 space-y-2">
                  {scoreData.weaknesses.map((weakness, index) => (
                    <li className="flex" key={index}>
                      <span className="text-red-500 mr-2">•</span>
                      <span className="text-sm text-neutral-700">{weakness}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </Card>
      </div>
      
      {/* Detailed Scores */}
      <Card className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-neutral-900 mb-4">Detailed Criterion Scores</h3>
          
          <div className="space-y-5">
            {scoreData.criterionScores.map((criterion, index) => {
              const scoreColor = 
                criterion.score >= 4 ? "bg-emerald-500" : 
                criterion.score >= 3 ? "bg-blue-500" : 
                criterion.score >= 2 ? "bg-amber-500" : 
                "bg-red-500";
                
              return (
                <div 
                  className={`${index < scoreData.criterionScores.length - 1 ? 'border-b border-neutral-200 pb-4' : ''}`} 
                  key={index}
                >
                  <div className="flex justify-between items-center mb-2">
                    <div>
                      <h4 className="font-medium text-neutral-800">{criterion.name}</h4>
                      <p className="text-sm text-neutral-500">{criterion.description}</p>
                    </div>
                    <div className="flex items-center">
                      <span className="mr-2 text-sm text-neutral-600">Score:</span>
                      <span className={`w-8 h-8 rounded-full ${scoreColor} text-white flex items-center justify-center font-medium`}>
                        {criterion.score}
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-neutral-700">{criterion.feedback}</p>
                </div>
              );
            })}
          </div>
        </div>
      </Card>
      
      {/* Action Plan */}
      <Card className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-neutral-900 mb-4">Recommended Next Steps</h3>
          
          <div className="space-y-4">
            {scoreData.recommendations.map((rec) => (
              <div className="flex items-start" key={rec.id}>
                <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary-100 text-primary-600 flex items-center justify-center font-medium text-sm">
                  {rec.id}
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-neutral-800">{rec.title}</h4>
                  <p className="mt-1 text-sm text-neutral-600">{rec.description}</p>
                  <div className="mt-2 flex">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                      {rec.priority} Priority
                    </span>
                  </div>
                </div>
              </div>
            ))}
            
            <div className="mt-6 flex flex-col items-center space-y-3">
              <Button 
                onClick={viewPremiumReport}
                variant="default"
                className="w-full sm:w-auto"
                size="lg"
              >
                <span className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  Get Premium Report
                </span>
              </Button>
              <p className="text-xs text-gray-500">
                Unlock detailed analysis and personalized recommendations
              </p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
