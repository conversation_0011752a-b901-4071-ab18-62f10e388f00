import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { googleLogout } from '@react-oauth/google';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default function UserMenu() {
  const queryClient = useQueryClient();
  const { data: user } = useQuery({ queryKey: ['/auth/me'] });

  const logoutMutation = useMutation({
    mutationFn: () => fetch('/auth/logout', { 
      method: 'POST',
      credentials: 'include'
    }),
    onSuccess: () => {
      googleLogout(); // Clear Google session
      queryClient.invalidateQueries({ queryKey: ['/auth/me'] });
    }
  });

  if (!user || !(user as any)?.user) return null;

  const userData = (user as any).user;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="flex items-center gap-2">
          {userData.avatarUrl && (
            <img
              src={userData.avatarUrl}
              alt={userData.name}
              className="w-6 h-6 rounded-full"
            />
          )}
          <span>{userData.name}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => logoutMutation.mutate()}>
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
