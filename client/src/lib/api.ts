import { apiRequest } from "./queryClient";
import { ProjectInfo, ProgressInfo, ScoreData } from "@/pages/Home";

export async function uploadProject(
  file: File,
  description: string,
  projectType: string
): Promise<{
  projectInfo: ProjectInfo;
  progress: ProgressInfo;
  scoreData: ScoreData;
  analysisId: number;
}> {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("description", description);
  formData.append("projectType", projectType);

  const response = await fetch("/api/analyze/upload", {
    method: "POST",
    body: formData,
    credentials: "include",
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to upload project");
  }

  return await response.json();
}

export async function analyzeGithubRepo(
  repoUrl: string,
  description: string,
  projectType: string
): Promise<{
  projectInfo: ProjectInfo;
  progress: ProgressInfo;
  scoreData: ScoreData;
  analysisId: number;
}> {
  const response = await apiRequest("POST", "/api/analyze/github", {
    repoUrl,
    description,
    projectType,
  });

  return await response.json();
}

export async function getScoreData(
  analysisId: string
): Promise<{
  projectInfo: ProjectInfo;
  scoreData: ScoreData;
}> {
  const response = await fetch(`/api/report/${analysisId}`, {
    credentials: "include",
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to get score data");
  }

  return await response.json();
}
