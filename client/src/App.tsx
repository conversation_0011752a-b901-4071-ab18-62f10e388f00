import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import AuthGuard from "@/components/AuthGuard";
import UserMenu from "@/components/UserMenu";
import NotFound from "@/pages/not-found";
import Home from "@/pages/Home";
import ReportPreview from "@/pages/ReportPreview";
import ReportPayment from "@/pages/ReportPayment";
import ReportProcessing from "@/pages/ReportProcessing";
import ReportDownload from "@/pages/ReportDownload";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/report/preview/:id">
        <AuthGuard>
          <ReportPreview />
        </AuthGuard>
      </Route>
      <Route path="/report/payment/:id">
        <AuthGuard>
          <ReportPayment />
        </AuthGuard>
      </Route>
      <Route path="/report/processing/:id">
        <AuthGuard>
          <ReportProcessing />
        </AuthGuard>
      </Route>
      <Route path="/report/download/:id">
        <AuthGuard>
          <ReportDownload />
        </AuthGuard>
      </Route>
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen">
        <header className="border-b bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <h1 className="text-xl font-semibold">VibeComplete</h1>
              <UserMenu />
            </div>
          </div>
        </header>
        <Router />
      </div>
      <Toaster />
    </QueryClientProvider>
  );
}

export default App;
