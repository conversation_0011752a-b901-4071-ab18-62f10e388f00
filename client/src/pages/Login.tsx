import { GoogleLogin } from '@react-oauth/google';
import { Card } from '@/components/ui/card';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';

export default function LoginPage() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const loginMutation = useMutation({
    mutationFn: async (credential: string) => {
      const response = await fetch('/auth/google', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ credential }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Login failed');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/auth/me'] });
      toast({
        title: "Welcome!",
        description: "You've been successfully signed in.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Sign in failed",
        description: error.message || "Please try again.",
        variant: "destructive",
      });
    }
  });

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-neutral-900">VibeComplete</h1>
          <p className="text-neutral-600 mt-2">
            Sign in to analyze your project's MVP readiness
          </p>
        </div>
        
        <div className="flex justify-center">
          <GoogleLogin
            onSuccess={(credentialResponse) => {
              if (credentialResponse.credential) {
                loginMutation.mutate(credentialResponse.credential);
              }
            }}
            onError={() => {
              toast({
                title: "Sign in failed",
                description: "Please try again.",
                variant: "destructive",
              });
            }}
            useOneTap
            theme="outline"
            size="large"
            text="continue_with"
          />
        </div>
        
        <p className="text-xs text-gray-500 text-center mt-4">
          By signing in, you agree to our Terms of Service and Privacy Policy
        </p>
      </Card>
    </div>
  );
}
