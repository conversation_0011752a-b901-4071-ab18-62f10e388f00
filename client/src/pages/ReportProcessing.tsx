import { useEffect, useState } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { CheckCircle, Clock, AlertCircle, FileText, CreditCard, Zap } from "lucide-react";

interface ProcessingStatus {
  pdfReady: boolean;
  paymentStatus: 'pending' | 'completed';
  fullReportGenerated: boolean;
  error?: string;
}

export default function ReportProcessing() {
  const [, setLocation] = useLocation();
  const params = useParams<{ id: string }>();
  const { toast } = useToast();
  const [status, setStatus] = useState<ProcessingStatus>({
    pdfReady: false,
    paymentStatus: 'pending',
    fullReportGenerated: false
  });
  const [progress, setProgress] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [currentStep, setCurrentStep] = useState("Verifying payment...");

  useEffect(() => {
    // Check if we're coming from Stripe success
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session_id');
    
    if (sessionId) {
      // Verify payment first
      verifyPayment(sessionId);
      // Remove session_id from URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else {
      // Start checking status immediately
      checkStatus();
    }

    // Set up polling interval
    const statusInterval = setInterval(checkStatus, 3000);
    
    // Set up progress simulation
    const progressInterval = setInterval(() => {
      setTimeElapsed(prev => prev + 1);

      // Simulate progress based on time elapsed, but only if we don't have a specific status-based progress
      if (!status.pdfReady) {
        setProgress(prev => {
          // Only increment if we're not at a status-based milestone
          // This prevents regression when status updates set specific values
          const currentProgress = prev;

          // If we're at a status milestone (25%, 75%), don't simulate further
          if (currentProgress === 25 || currentProgress === 75) {
            return currentProgress;
          }

          // Simulate gradual progress, but respect status-based milestones
          let targetProgress = 90; // Default cap
          if (status.paymentStatus === 'completed' && !status.fullReportGenerated) {
            targetProgress = 70; // Cap before 75% milestone
          } else if (status.fullReportGenerated && !status.pdfReady) {
            targetProgress = 95; // Cap before 100%
          }

          const newProgress = Math.min(currentProgress + 1, targetProgress);
          return newProgress;
        });
      }
    }, 2000); // Slower simulation to reduce conflicts

    return () => {
      clearInterval(statusInterval);
      clearInterval(progressInterval);
    };
  }, [params.id]);

  const verifyPayment = async (sessionId: string) => {
    try {
      setCurrentStep("Verifying payment...");
      const response = await apiRequest(
        "GET",
        `/api/report/verify-payment/${params.id}?session_id=${sessionId}`
      );
      
      if (response.ok) {
        toast({
          title: "Payment Successful",
          description: "Your payment has been processed. Generating your comprehensive report...",
          variant: "default"
        });
        setCurrentStep("Generating comprehensive report...");
      }
    } catch (error) {
      console.error("Error verifying payment:", error);
      toast({
        title: "Payment Verification Error",
        description: "There was an issue verifying your payment. Please contact support.",
        variant: "destructive"
      });
    }
  };

  const checkStatus = async () => {
    try {
      const response = await apiRequest("GET", `/api/report/status/${params.id}`);
      
      if (response.ok) {
        const responseData = await response.json();
        const data = responseData.data || responseData;
        setStatus(data);

        if (data.error) {
          setCurrentStep("Error occurred during processing");
          toast({
            title: "Processing Error",
            description: data.error,
            variant: "destructive"
          });
        } else if (data.pdfReady) {
          setProgress(prev => Math.max(prev, 100)); // Ensure we only move forward
          setCurrentStep("Report ready for download!");

          // Wait a moment for user to see completion, then redirect
          setTimeout(() => {
            setLocation(`/report/download/${params.id}`);
          }, 2000);
        } else if (data.fullReportGenerated) {
          setCurrentStep("Converting to PDF...");
          setProgress(prev => Math.max(prev, 75)); // Ensure we only move forward
        } else if (data.paymentStatus === 'completed') {
          setCurrentStep("Generating comprehensive report...");
          setProgress(prev => Math.max(prev, 25)); // Ensure we only move forward
        }
      } else if (response.status === 403) {
        // Payment required
        setLocation(`/report/payment/${params.id}`);
      } else if (response.status === 404) {
        toast({
          title: "Analysis Not Found",
          description: "The requested analysis could not be found.",
          variant: "destructive"
        });
        setLocation('/');
      }
    } catch (error) {
      console.error("Error checking status:", error);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStepIcon = (step: string) => {
    if (step.includes("Verifying payment")) return <CreditCard className="w-5 h-5" />;
    if (step.includes("Generating")) return <Zap className="w-5 h-5" />;
    if (step.includes("Converting")) return <FileText className="w-5 h-5" />;
    if (step.includes("ready")) return <CheckCircle className="w-5 h-5 text-green-500" />;
    if (step.includes("Error")) return <AlertCircle className="w-5 h-5 text-red-500" />;
    return <Clock className="w-5 h-5" />;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <Card className="p-8 text-center">
            <div className="mb-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                {getStepIcon(currentStep)}
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Generating Your MVP Report
              </h1>
              <p className="text-gray-600">
                We're creating your comprehensive analysis with actionable insights
              </p>
            </div>

            <div className="mb-8">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">{currentStep}</span>
                <span className="text-sm text-gray-500">{progress}%</span>
              </div>
              <Progress value={progress} className="w-full h-3" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
                <CreditCard className={`w-5 h-5 ${status.paymentStatus === 'completed' ? 'text-green-500' : 'text-gray-400'}`} />
                <div className="text-left">
                  <div className="font-medium text-sm">Payment</div>
                  <div className="text-xs text-gray-500">
                    {status.paymentStatus === 'completed' ? 'Verified' : 'Processing'}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
                <Zap className={`w-5 h-5 ${status.fullReportGenerated ? 'text-green-500' : 'text-gray-400'}`} />
                <div className="text-left">
                  <div className="font-medium text-sm">Analysis</div>
                  <div className="text-xs text-gray-500">
                    {status.fullReportGenerated ? 'Complete' : 'Generating'}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
                <FileText className={`w-5 h-5 ${status.pdfReady ? 'text-green-500' : 'text-gray-400'}`} />
                <div className="text-left">
                  <div className="font-medium text-sm">PDF</div>
                  <div className="text-xs text-gray-500">
                    {status.pdfReady ? 'Ready' : 'Creating'}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg mb-6">
              <h3 className="font-medium text-blue-900 mb-2">What's Being Generated</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Executive summary with actionable insights</li>
                <li>• Detailed scoring breakdown with code examples</li>
                <li>• Technology stack analysis and recommendations</li>
                <li>• Deployment checklist and testing strategy</li>
                <li>• Personalized roadmap for MVP improvement</li>
              </ul>
            </div>

            <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>Time elapsed: {formatTime(timeElapsed)}</span>
              </div>
              <div className="text-gray-300">•</div>
              <div>Estimated: 30-60 seconds</div>
            </div>

            {status.error && (
              <div className="mt-6">
                <Button 
                  variant="outline" 
                  onClick={() => window.location.reload()}
                  className="w-full"
                >
                  Retry Processing
                </Button>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
